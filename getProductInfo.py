from curl_cffi import requests
from redis import Redis

import redis_client
import json
from bs4 import BeautifulSoup

guang_r5 = Redis(host='************', port=6379, db=3, password="AirSmile@1688", )

import re

REDIS_COOKIES_KEY = 'MAG-RU_cookies'
REDIS_COOKIES_KEY_BUYER_OZON = 'ozon_buyer_cookies'
REDIS_COOKIES_KEY_HEADERS_OZON = 'ozon_buyer_headers'




proxies = {
'http': 'socks5://lens:ls3903850@**************:23481',
'https': 'socks5://lens:ls3903850@**************:23481'
}

headers = {
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,vi;q=0.8,en-US;q=0.7,en;q=0.6",
            "priority": "u=0, i",
            "sec-ch-ua:": '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            "sec-ch-ua-mobile": '?0',
            "^sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "service-worker-navigation-preload": "true",
            "upgrade-insecure-requests": "1",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
            "cookie": '__Secure-ab-group=55; __Secure-ext_xcid=6246bf8c232dc888d203da155c10b981; contentId=320167; cf_clearance=NPhZDh8akzhyDwVjlL1zbOrfxjAoi2K6YYKemaDqgNs-1714212816-*******-NNthBxnxi31y6nnuJCYetoxkN8RLP1tgxufg36F8.FF_cWvmYZOz7JUiOaECQCFYasr5bo_NrmcpViMZSr3YgQ; cf_clearance=WaO5mGugICPHVI6Dw3NS8A2ZacmCKAHRSuRqhGkvUVs-1716180221-*******-.n62fXE4gG5CgyTpvzvpEBM3up67UFpsjdnC0XuvR_PWXkgifK97XNQTZrJgPdElw2dyR3CLTrUiX2_YVjOV2w; abt_data=f6f772bd2e91f26bf432a988b4a6d98a:afe7e2117fdcf7fd9742002da7a277f2be1e62002cfd6a507c1cabe1879240b315662bda27d5277f2d3a7231254cb174283d4c3e809d7d9e4e84e66471c45c71e1ff6bd2b8f7f4b88eaf2935ccc70f419e507375a0cfd354749721801541358b03b57e9d0e7aefd405ff5ad93d2bdee786f2464c1ab606ca1e6fe261deb19302c2df87b7de0b69421f60b7a7d215166daa501d361011b1b8c3a332a10252a8aec178d426dfb0ea55b84203ac826c8aca9d7ffaca3da9dfc64a77dcf0dfe34e888f6ef41d4857a411f849c6e64f5ec2dd2009187ed26c0092a1ffab810047eeb3e169ebbdd4f7db8db771d2207cf41f0c38479164aebd6e8faa598f6b735fe9aaca63b5b9293bbb1d2c553e388ac363b5bd877c20473827f6ec6dfc2f20f7e20e8deefc14c609ffd1790d8280f0eb05b082bb0d10146927758e75cef12d344c1b; __Secure-user-id=0; bacntid=; is_cookies_accepted=1; abt_data=7.m8yVjLnu5abVfCWJINP00oOhI-w0HQGAvxLtvSYSxtRLXsiXQL3XXSF5_LduvklfrnEwg3lwzUf6Q7UVXrZRgC8vvVrfgGsLAk32xMuNKyDQBfFaWyTq1yIVRN8PWS9PPGeSzbNe4AyJV4HH67XwpSKYCiAvUswut61qE7Kf8i0W7qwLG_ibVd-Q-W0r3xiIYK3EdOg3glGBlQDgfqxakyX85YOnF4HXfBn7hwxo8LLlOXYrx0ho6erooZ0mOxjykcuQy-4pV-GeT_Byxd6ftYa-ZwblFAXJ4b00cNJU8lTp9x_-EqExYML2wv1egb40Q6DzKB5wunqG5slUAjaN_ZJCgZigS8ppYMcbGR3Dspe3dvFZ1l9DmQva47Gd1OtwJtrSALvoWXpXv4Ab_gScjVxkAN25bsKUNYKobLJH19ykh8bdd0GJaH4EzMLUI02454BNcQ1utOK7DjqdlshxKeNvV9OFLUKq5ijHIHw4MyGhoFwS8aVxKlNSebO0riTwxIxfgziw9SaJfNXQN7HFFA; rfuid=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; __Secure-ETC=a06241ba43d2db44b2a9f89a9221aa98; __Secure-access-token=6.0.MigsiC3HQRaygakMGd55gw.55.Aez4c7la_LPXaiuP6RIfUvMFYmj4HzyWrk2kNH5u81Tv9vNW0VVUqtTViCmcvnJL7g..20241117051700.Gz7nPNv1hmxhzfKZbODTUbpFbiHPgn9Om9EGDorBQ_4.15271aaa07b1557c9; __Secure-refresh-token=6.0.MigsiC3HQRaygakMGd55gw.55.Aez4c7la_LPXaiuP6RIfUvMFYmj4HzyWrk2kNH5u81Tv9vNW0VVUqtTViCmcvnJL7g..20241117051700.mLov6XV5wUPQLPiuCFneaOmUwy9pvSM0x9oq47B_L0w.191b760c18442d333; TS013595b9=0187c00a18c06374e572a891c68addcbb806f54acce4281dc6274f27f5c950f15a4192aa01b28fb086c3a175ffa6f12fa9a69d8d62; TS015d2969=0187c00a18c06374e572a891c68addcbb806f54acce4281dc6274f27f5c950f15a4192aa01b28fb086c3a175ffa6f12fa9a69d8d62; xcid=8c130db00df0e67beda479ddd5840132',
        }
def getOzonProductInfo(product_id):
    # 判断是否有缓存
    product_info = guang_r5.get(f"ozon_product_info_{product_id}")
    if product_info:
        return json.loads(product_info)

    try:
        # cookies_dict_ozon_buyer = json.loads(guang_r5.get(REDIS_COOKIES_KEY_BUYER_OZON))
        # headers_dict_ozon_buyer = json.loads(guang_r5.get(REDIS_COOKIES_KEY_HEADERS_OZON))
        client = requests.Session()
        client.proxies = proxies
        client.headers = headers
        response = client.get('https://www.ozon.ru')
        if response.status_code == 200:
            print(1)
        #client.proxies = proxies
        url = 'https://www.ozon.ru/product/{}/'.format(product_id)

        response = client.get(url)
        if response.status_code == 200:
            # 请求完后,将cookies和headers存入redis
            # cookies_dict = {}
            # for cookie in client.cookies:
            #     cookies_dict.update({cookie.name: cookie.value})

            # guang_r5.set(REDIS_COOKIES_KEY_BUYER_OZON, json.dumps(cookies_dict))
            # guang_r5.set(REDIS_COOKIES_KEY_HEADERS_OZON, json.dumps(dict(client.headers)))

            product_info = response.text

            # response=client.get('https://www.ozon.ru/search/?deny_category_prediction=true\u0026from_global=true\u0026text=%D0%91%D0%BE%D1%81%D0%BE%D0%BD%D0%BE%D0%B6%D0%BA%D0%B8\u002bMascotte\u0026product_id=257876052')
            # if response.status_code == 200:
            #     product_info = response.text
            # 店铺
            seller_id_re = re.search(r'(?<=href="/seller/).+?(?=/)', product_info)
            if seller_id_re:
                seller_id = seller_id_re.group()
                seller_url = "https://www.ozon.ru/seller/" + seller_id + "/"

            # 价格
            price_re = re.search(r'(?<="price":")\d+?(?=")', product_info)
            if price_re:
                price = int(price_re.group())

            # 主图
            soup = BeautifulSoup(product_info, 'html.parser')
            imgUrl = soup.find('link', attrs={'as': 'image'}).get('href')

            result = {
                'productId': int(product_id),
                "sellerId": str(seller_id),
                "price": price,
                "img": imgUrl
            }
            # 将result存入redis,3个小时过期时间
            guang_r5.set(f"ozon_product_info_{product_id}", json.dumps(result), ex=10800)

            print(result)
            return result
    except Exception as e:
        print(product_id, e)


def getWbProductInfo(product_id):
    # 判断是否有缓存
    # product_info = guang_r5.get(f"wb_product_info_{product_id}")
    # if product_info:
    #     return json.loads(product_info)
    try:
        url = 'https://card.wb.ru/cards/v2/detail?appType=1&curr=rub&dest=-1257786&spp=30&ab_testing=false&nm={}'.format(
            product_id)
        response = requests.get(url)
        if response.status_code == 200:
            product_info = response.json()
            if len(product_info['data']['products'][0]['sizes'][0]['stocks']) == 0:
                print(product_id, " Product is out of stock")
                return False
            photo = ''
            for i in range(1, 20):
                try:
                    product_id = str(product_id)
                    resp = requests.get(
                        "https://basket-{}.wbbasket.ru/vol{}/part{}/{}/images/big/1.webp".format(
                            str(i), product_id[:4], product_id[:6], product_id
                        )
                    )
                    if resp.status_code == 200:
                        photo = resp.url
                except:
                    pass
            result = {
                'productId': int(product_id),
                "sellerId": str(product_info['data']['products'][0]['supplierId']),
                "price": int(product_info['data']['products'][0]['sizes'][0]['price']['total']) / 100,
                "img": photo
            }
            print(result)
            # 将result存入redis,3个小时过期时间
            guang_r5.set(f"wb_product_info_{product_id}", json.dumps(result), ex=10800)

            return result
    except Exception as e:
        print(product_id, e)


def main():
    ozon_product_ids = [
        "1675808365",
    ]
    product_ids = [
        '1727802796'
    ]
    for product_id in product_ids:
        getOzonProductInfo(product_id)


if __name__ == '__main__':
    main()
