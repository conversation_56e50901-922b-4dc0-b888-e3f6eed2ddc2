import redis
import requests
import datetime

EXCHANGE_RATE_EXTRA_POINTS = 0.06

class ExchangeRate:
    def __init__(self, rdb=None):
        if rdb is None:
            self.rdb = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        else:
            self.rdb = rdb
        self.app_id = "5d3b75dfbcb34dc2a3c54153dc2f027a"
        self.base_url = f"https://openexchangerates.org/api/latest.json?app_id={self.app_id}"


    def _get_exchange_rate_today(self, from_currency, to_currency):
        response = requests.get(self.base_url)
        data = response.json()
        if "rates" in data:
            rates = data["rates"]
            if from_currency in rates and to_currency in rates:
                exchange_rate = rates[to_currency] / rates[from_currency]
                print(f"The exchange rate from {from_currency} to {to_currency} is {exchange_rate}")
                return exchange_rate
            else:
                print("Invalid currency code")
        else:
            print("Failed to retrieve exchange rate data")

    def _get_exchange_rate_by_date(self, date, from_currency, to_currency):
        # 构建Redis键名
        redis_key = f"exchange_rate:{date}:{from_currency}:{to_currency}"

        # 检查Redis中是否存在相应的数据
        if self.rdb.exists(redis_key):
            exchange_rate = float(self.rdb.get(redis_key))
            exchange_rate_add = round((exchange_rate * (1 + EXCHANGE_RATE_EXTRA_POINTS)), 5)
            print(
                f"The exchange rate from {from_currency} to {to_currency} on {date} (from Redis) is {exchange_rate} ,addRate is {exchange_rate_add}")
            return exchange_rate_add
        else:
            base_url = self.base_url.replace('latest', 'historical/'+date)
            response = requests.get(base_url)
            data = response.json()
            if "rates" in data:
                rates = data["rates"]
                if from_currency in rates and to_currency in rates:
                    exchange_rate = round(rates[to_currency] / rates[from_currency], 5)
                    # 将数据存储到Redis中
                    self.rdb.set(redis_key, exchange_rate)
                    exchange_rate_add = round((exchange_rate * (1 + EXCHANGE_RATE_EXTRA_POINTS)),5)
                    print(
                        f"The exchange rate from {from_currency} to {to_currency} on {date} is {exchange_rate} ,addRate is {exchange_rate_add}")
                    return exchange_rate_add
                else:
                    print("Invalid currency code")
            else:
                print("Failed to retrieve exchange rate data")

    def get_rate(self):
        now = datetime.datetime.now()
        # 如果超过0-5点,取前一天的汇率
        if now.hour < 5:
            today = datetime.date.today() - datetime.timedelta(days=1)
        else:
            today = datetime.date.today() - datetime.timedelta(days=0)
        date_str = today.strftime("%Y-%m-%d")
        exRate = self._get_exchange_rate_by_date(date_str, "RUB", "CNY")
        if exRate is None:
            date_str = (today - datetime.timedelta(days=1)).strftime("%Y-%m-%d")
            exRate = self._get_exchange_rate_by_date(date_str, "RUB", "CNY")
        return round(exRate * 100, 3)


if __name__ == '__main__':
    print("Start testing...")
    ExchangeRate().get_rate()
     # 使用您的Open Exchange Rates App ID调用函数
    #用datetime生成今天的日期"2024-07-28"这种格式的字符串

    # today = datetime.date.today()
    # date_str = today.strftime("%Y-%m-%d")
    # get_exchange_rate_by_date(date_str, "RUB", "CNY")
    #
    # 使用您的Open Exchange Rates App ID调用函数
    # get_exchange_rate_today("RUB", "CNY")