Metadata-Version: 2.1
Name: icecream
Version: 2.1.4
Summary: Never use print() to debug again; inspect variables, expressions, and program execution with a single, simple function call.
Home-page: https://github.com/gruns/icecream
Author: <PERSON><PERSON><PERSON> Grunseid
Author-email: <EMAIL>
License: MIT
Platform: any
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Libraries
Classifier: Development Status :: 5 - Production/Stable
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Programming Language :: Python :: Implementation :: CPython
License-File: LICENSE.txt
Requires-Dist: colorama >=0.3.9
Requires-Dist: pygments >=2.2.0
Requires-Dist: executing >=2.1.0
Requires-Dist: asttokens >=2.0.1

Information and documentation can be found at https://github.com/gruns/icecream.
