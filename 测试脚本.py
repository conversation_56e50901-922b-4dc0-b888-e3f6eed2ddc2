#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
装箱标签SKU处理器测试脚本
用于验证程序各个功能模块的正确性
"""

import pandas as pd
import fitz
from pyzbar import pyzbar
import cv2
import numpy as np
from PIL import Image
import io
from pathlib import Path
import sys

def test_dependencies():
    """测试依赖包是否正确安装"""
    print("🔍 测试依赖包...")
    
    try:
        import pandas
        print(f"✅ pandas {pandas.__version__}")
    except ImportError:
        print("❌ pandas 未安装")
        return False
    
    try:
        import fitz
        print(f"✅ PyMuPDF {fitz.version[0]}")
    except ImportError:
        print("❌ PyMuPDF 未安装")
        return False
    
    try:
        import pyzbar
        print(f"✅ pyzbar 已安装")
    except ImportError:
        print("❌ pyzbar 未安装")
        return False
    
    try:
        import cv2
        print(f"✅ opencv-python {cv2.__version__}")
    except ImportError:
        print("❌ opencv-python 未安装")
        return False
    
    try:
        from PIL import Image
        print(f"✅ Pillow 已安装")
    except ImportError:
        print("❌ Pillow 未安装")
        return False
    
    try:
        import reportlab
        print(f"✅ reportlab 已安装")
    except ImportError:
        print("❌ reportlab 未安装")
        return False
    
    return True

def test_file_existence():
    """测试输入文件是否存在"""
    print("\n📁 测试文件存在性...")
    
    excel_file = "file/wb/31851009-装箱.xlsx"
    pdf_file = "file/wb/31851009-装箱-标签.pdf"
    
    if Path(excel_file).exists():
        print(f"✅ Excel文件存在: {excel_file}")
    else:
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    if Path(pdf_file).exists():
        print(f"✅ PDF文件存在: {pdf_file}")
    else:
        print(f"❌ PDF文件不存在: {pdf_file}")
        return False
    
    return True

def test_excel_data():
    """测试Excel数据读取和格式"""
    print("\n📊 测试Excel数据...")
    
    try:
        df = pd.read_excel("file/31851009-装箱.xlsx")
        print(f"✅ Excel文件读取成功，共 {len(df)} 行数据")
        
        # 检查必要的列
        required_columns = ['Баркод товара', 'Кол-во товаров', 'ШК короба']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ 缺少必要的列: {missing_columns}")
            return False
        else:
            print("✅ 所有必要的列都存在")
        
        # 检查数据质量
        print(f"✅ 产品种类: {df['Баркод товара'].nunique()} 种")
        print(f"✅ 箱码数量: {df['ШК короба'].nunique()} 个")
        print(f"✅ 数量范围: {df['Кол-во товаров'].min()} - {df['Кол-во товаров'].max()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel数据测试失败: {str(e)}")
        return False

def test_pdf_structure():
    """测试PDF文件结构和二维码"""
    print("\n📄 测试PDF结构...")
    
    try:
        doc = fitz.open("file/wb/31851009-装箱-标签.pdf")
        print(f"✅ PDF文件打开成功，共 {len(doc)} 页")
        
        # 测试前几页的二维码识别
        qr_detected = 0
        for page_num in range(min(5, len(doc))):
            page = doc[page_num]
            
            # 转换为图像
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            img_data = pix.tobytes('png')
            pil_image = Image.open(io.BytesIO(img_data))
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            # 检测二维码
            qr_codes = pyzbar.decode(cv_image)
            if qr_codes:
                qr_detected += 1
                qr_data = qr_codes[0].data.decode('utf-8', errors='ignore')
                print(f"✅ 第 {page_num + 1} 页检测到二维码: {qr_data}")
        
        total_pages = len(doc)
        doc.close()

        if qr_detected > 0:
            print(f"✅ 二维码检测正常，测试了 {min(5, total_pages)} 页，检测到 {qr_detected} 个")
            return True
        else:
            print("❌ 未检测到任何二维码")
            return False
            
    except Exception as e:
        print(f"❌ PDF结构测试失败: {str(e)}")
        return False

def test_data_matching():
    """测试数据匹配逻辑"""
    print("\n🔗 测试数据匹配...")
    
    try:
        # 读取Excel数据
        df = pd.read_excel("file/31851009-装箱.xlsx")
        excel_data = {}
        for _, row in df.iterrows():
            box_code = str(row['ШК короба']).strip()
            sku = str(row['Баркод товара']).strip()
            quantity = int(row['Кол-во товаров'])
            excel_data[box_code] = {'sku': sku, 'quantity': quantity}
        
        # 读取PDF并检查匹配
        doc = fitz.open("file/wb/31851009-装箱-标签.pdf")
        matched = 0
        unmatched = 0
        
        for page_num in range(min(10, len(doc))):  # 测试前10页
            page = doc[page_num]
            
            # 检测二维码
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            img_data = pix.tobytes('png')
            pil_image = Image.open(io.BytesIO(img_data))
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            qr_codes = pyzbar.decode(cv_image)
            
            if qr_codes:
                qr_data = qr_codes[0].data.decode('utf-8', errors='ignore').strip()
                if qr_data in excel_data:
                    matched += 1
                    sku_info = excel_data[qr_data]
                    print(f"✅ 第 {page_num + 1} 页匹配成功: {qr_data} -> SKU: {sku_info['sku']}, 数量: {sku_info['quantity']}")
                else:
                    unmatched += 1
                    print(f"❌ 第 {page_num + 1} 页匹配失败: {qr_data}")
        
        total_pages = len(doc)
        doc.close()

        print(f"✅ 匹配测试完成: 成功 {matched}, 失败 {unmatched}")
        return matched > 0
        
    except Exception as e:
        print(f"❌ 数据匹配测试失败: {str(e)}")
        return False

def test_output_verification():
    """测试输出文件验证"""
    print("\n📤 测试输出文件...")
    
    output_file = "file/wb/31851009-装箱-标签_processed.pdf"
    
    if not Path(output_file).exists():
        print(f"❌ 输出文件不存在: {output_file}")
        return False
    
    try:
        doc = fitz.open(output_file)
        print(f"✅ 输出PDF文件打开成功，共 {len(doc)} 页")
        
        # 检查前几页是否包含SKU信息
        pages_with_sku = 0
        for page_num in range(min(5, len(doc))):
            page = doc[page_num]
            text = page.get_text()
            
            if "SKU:" in text:
                pages_with_sku += 1
                print(f"✅ 第 {page_num + 1} 页包含SKU信息")
                # 显示文本内容
                lines = text.strip().split('\n')
                for line in lines:
                    if line.strip():
                        print(f"   {line}")
        
        total_pages = len(doc)
        doc.close()

        if pages_with_sku > 0:
            print(f"✅ SKU信息添加正常，测试了 {min(5, total_pages)} 页，{pages_with_sku} 页包含SKU信息")
            return True
        else:
            print("❌ 未发现SKU信息")
            return False
            
    except Exception as e:
        print(f"❌ 输出文件验证失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 装箱标签SKU处理器测试")
    print("=" * 50)
    
    tests = [
        ("依赖包测试", test_dependencies),
        ("文件存在性测试", test_file_existence),
        ("Excel数据测试", test_excel_data),
        ("PDF结构测试", test_pdf_structure),
        ("数据匹配测试", test_data_matching),
        ("输出文件验证", test_output_verification),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序运行正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
        return False

if __name__ == "__main__":
    main()
