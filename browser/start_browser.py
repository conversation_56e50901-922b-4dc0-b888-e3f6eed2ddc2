import json
import sys
import atexit
from handler.ClientOpenHandler import open_client
from handler.EnvOpenHandler import open_env
from handler.EnvCloseHandler import close_env
from handler.ClientCloseHandler import close_client
from playwright import sync_api
from info import BuyerInfo, InfoTask, ShuadanTask, ReviewTask,ziti_address


def open_browser(buyerInfo):
    #启动客户端
    # open_result = open_client(group_code="11402616")
    open_result = open_client(group_code="13812575")
    if not open_result:
        sys.exit()
    #打开环境，获取playwright调试端口
    env_open_result = open_env( container_code=buyerInfo.device_id)
    if not env_open_result.success:
        sys.exit()
    port =json.loads(env_open_result.result).get("debuggingPort")
    playwright = sync_api.sync_playwright().start()
    browser = playwright.chromium.connect_over_cdp("http://127.0.0.1:" + str(port))


    atexit.register(browser.close)#关闭浏览器
    atexit.register(playwright.stop)#关闭浏览器
    atexit.register(close_env, container_code=buyerInfo.device_id)#关闭环境
    atexit.register(close_client)#关闭关闭客户端

    browser_context = browser.contexts[0]

    page = browser_context.pages[0]
    # check_netword_page = browser_context.new_page()
    # check_netword_page.goto("http://127.0.0.1:" + str(port))
    return page,close_client,playwright,browser,close_env,browser_context


if __name__ == '__main__':

    buyerInfo = BuyerInfo('79153379453')
    page,close_client,playwright,browser,close_env,browser_context = open_browser(buyerInfo)
    print('1')