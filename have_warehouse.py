import json
import random
from datetime import datetime, timedelta
import pytz
import requests
import time


class HaveWarehouse:
    def __init__(self, authorize):
        self.auth = authorize
        self.user_agent = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36'}
        self.cookies = {
            'Cookie': f'wb-id=gYH6RYh243JA-5jvpxvm1N__AAABkUwgGuu56KNK8LAj3p89sJAFJYvwzVgTblRTibXPjSI-jb5wCjAxNjAyOWJiLTJlNTQtNDgyNi04OGMyLTg1YWE4MGQzNjJmNw; wb-pid=gYFgHgb-gO5AR4C8EDF9t3oxAAABkUwgGuvpq14mUPjZJ9BNhBYcQhI-TUJwRD9-zm0-CIKiBHHeRw; wbx-validation-key=3d3da75c-55e2-4e52-89c8-69e3a240ed83; x-supplier-id-external=47b418ca-bf65-4755-bd58-0fffd9985525; x-supplier-id=47b418ca-bf65-4755-bd58-0fffd9985525; ___wbu=8b26fa7c-3768-4cdc-8d0b-692afcbffa59.1723907110; WBTokenV3={self.auth}; locale=zh; captchaid=1730334973|d0270c01fdc64444b7e00c1aa7b6c310|00000000000001hVsLZdrf00000000000001hVsVQPFD00000000000001hVvOhAJK00000000000001hVuqQO7g00000000000001hVs7gKvH00000000000001hVtVsvEb00000000000001hVs7xhDS|qnx3OB1bRWn2FBHUggQNai1hq68O52Jjd7nXknmZuUZ; external-locale=zh; current_feature_version=1c40f330-0052-4a60-8fe4-2154b484c498; __zzatw-wb=MDA0dC0cTHtmcDhhDHEWTT17CT4VHThHKHIzd2UqQ2UmYUldJkZHSWtlTlNCLGYbcRVNCA00PVpyIg9bOSVYCBI/CyYgEwhvK1EKE2JBSnNvG382XRw5YxELGX46Y11GRzcVJHt1EmxkCh5MVAw7FmBtEU0oP0dWVVY0XS1BPRRhcEpxeCs8ZyMcS1kjeA9VMyVNFQhwVE8PQF9GdidlJS0tUikSGmIPR1draF1QQSRaDHF/TQl6MjAbRWYkYVBdJ0VYVQgpIhhzXWcgR0BNRzM1N1p0K2EcFyURGFE/RmhOUy4vYgo4RxgvS0Blb2wpYhw5YxEVFT91F1lGQTZcGkt1ZS8MOTprbCRSUUNLY3waCmsvGhh5dCdXCQ5jREV2eyUtMWYnfEspNR0RMl5XVTQ7Z0FUf00IDjU7ZXQiXxYIFRFNKD9HVlVWNF0tQQsSFUFCdS5aOB9TZUxWI0paUnZXTRZ/aCMNPUEXRkp1eC49ag9bOSFUDSAORGkLG2k2ZxZJPBpyM2llbXQqUlFRWiZFXlEJJxwZfnApUHt1Jw4JKmUzLS1ZGAgfY3glGWtyZg==6saOUA==; __zzatw-wb=MDA0dC0cTHtmcDhhDHEWTT17CT4VHThHKHIzd2UqQ2UmYUldJkZHSWtlTlNCLGYbcRVNCA00PVpyIg9bOSVYCBI/CyYgEwhvK1EKE2JBSnNvG382XRw5YxELGX46Y11GRzcVJHt1EmxkCh5MVAw7FmBtEU0oP0dWVVY0XS1BPRRhcEpxeCs8ZyMcS1kjeA9VMyVNFQhwVE8PQF9GdidlJS0tUikSGmIPR1draF1QQSRaDHF/TQl6MjAbRWYkYVBdJ0VYVQgpIhhzXWcgR0BNRzM1N1p0K2EcFyURGFE/RmhOUy4vYgo4RxgvS0Blb2wpYhw5YxEVFT91F1lGQTZcGkt1ZS8MOTprbCRSUUNLY3waCmsvGhh5dCdXCQ5jREV2eyUtMWYnfEspNR0RMl5XVTQ7Z0FUf00IDjU7ZXQiXxYIFRFNKD9HVlVWNF0tQQsSFUFCdS5aOB9TZUxWI0paUnZXTRZ/aCMNPUEXRkp1eC49ag9bOSFUDSAORGkLG2k2ZxZJPBpyM2llbXQqUlFRWiZFXlEJJxwZfnApUHt1Jw4JKmUzLS1ZGAgfY3glGWtyZg==6saOUA==; cfidsw-wb=d/8Y0ugyOi60hlIMyq0rFcD0kc7lGwgKvrns4kngu1vZu0+rozSPV8hwfPVcKve2W01CCqK5aKFSeP9+bqp90mrWhozV70vzGP71PnaTzTPunbVOpDwi/BT6nSpabThEvMGLaEN4XFoGQw02wPneucd4DCgFJW4mlBNWKD0NTQ==; cfidsw-wb=d/8Y0ugyOi60hlIMyq0rFcD0kc7lGwgKvrns4kngu1vZu0+rozSPV8hwfPVcKve2W01CCqK5aKFSeP9+bqp90mrWhozV70vzGP71PnaTzTPunbVOpDwi/BT6nSpabThEvMGLaEN4XFoGQw02wPneucd4DCgFJW4mlBNWKD0NTQ=='}
        self.session = requests.Session()
        self.session.headers.update(self.user_agent)
        self.preorderIds = []

    def is_auth(self):
        self.session.headers.update({'Content-Type': 'application/json'})
        self.session.headers.update({'Authorizev3': self.auth})
        self.session.headers.update(self.cookies)
        response = self.session.post('https://seller.wildberries.ru/upgrade-cookie-authv3')
        if response.status_code == 200:
            del self.session.headers['Authorizev3']
            return True
        else:
            return False

    def list_supplies(self):
        if self.is_auth():
            data = '{"params":{"pageNumber":1,"pageSize":100,"sortBy":"createDate","sortDirection":"desc","statusId":-2},"jsonrpc":"2.0","id":"json-rpc_26"}'
            self.session.headers.update({'Content-Type': 'application/json'})
            response = self.session.post(
                'https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/listSupplies',
                data=data)
            if response.status_code == 200:
                data = response.json()
                for supply in data['result'].get('data', []):
                    if supply.get('statusId') == -1:
                        self.preorderIds.append(supply.get('preorderId'))
            else:
                print(f'\n {response.status_code}')

    def get_acceptance_costs(self, coefficient):
        moscow_tz = pytz.timezone('Europe/Moscow')
        for preorderId in self.preorderIds:
            # data_json = {'params': {'dateFrom': '2024-10-23T12:07:14.156Z','dateTo': '2024-11-21T21:00:00.000Z','preorderId': preorderId,'supplyId': ''},'jsonrpc': '2.0', 'id': 'json-rpc_30'}
            data_obj = json.loads(
                '{"params":{"dateFrom":"2024-10-22T12:07:14.156Z","dateTo":"2024-11-24T21:00:00.000Z","preorderID":12355466,"supplyId":null},"jsonrpc":"2.0","id":"json-rpc_30"}')
            data_obj['params']['preorderID'] = preorderId
            now = datetime.now(moscow_tz)
            data_obj['dateFrom'] = now.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
            data_obj['dateTo'] = (now + timedelta(days=30)).strftime('%Y-%m-%dT21:00:00.000Z')

            response = self.session.post(
                'https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/getAcceptanceCosts',
                data=json.dumps(data_obj))
            if response.status_code == 200:
                for cost in response.json()['result'].get('costs', []):
                    if -1 < cost.get('coefficient') < coefficient:
                        print(f'计划:{preorderId},倍数：{cost.get("coefficient")},预约价格：{cost.get("cost")} ,可预约日期：:{cost.get("date")}')
                        self.add_plan(preorderId, cost.get('date'))
                        self.preorderIds.remove(preorderId)
            else:
                time.sleep(random.uniform(1.2, 5.4))

    def add_plan(self, preorder_id, delivery_date):
        data_obj = json.loads('{"params":{"preOrderId":null,"deliveryDate":"","monopalletCount":5,"supplierAssignUUID":null},"jsonrpc":"2.0","id":"json-rpc_40"}')
        data_obj['params']['preOrderId'] = preorder_id
        data_obj['params']['deliveryDate'] = delivery_date
        response = self.session.post('https://seller-supply.wildberries.ru/ns/sm/supply-manager/api/v1/plan/add',data=json.dumps(data_obj))
        if response.status_code == 200:
            print(f'添加计划成功:{preorder_id}')
        else:
            print(f'添加计划失败:{preorder_id}')

if __name__ == '__main__':
    auth = '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    hw = HaveWarehouse(auth)
    hw.list_supplies()
    while True:
        hw.get_acceptance_costs(2)
        time.sleep(random.uniform(0.2, 1))