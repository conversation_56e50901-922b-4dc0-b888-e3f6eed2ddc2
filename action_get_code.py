import base64

import os
import re
import time
import traceback
from io import BytesIO


import redis
import requests
from PIL import Image
from datetime import datetime
from pyzbar.pyzbar import decode
from playwright.async_api import Page
from browser.start_browser import open_browser
from change_ip import ChangeIP
from db.client_mongdb import login_mongdb_review
from info import BuyerInfo, InfoTask, ShuadanTask, ReviewTask, ziti_address
import json
from barcode import ImageWriter,Code128
from lendian.api import LDApi

m_db = login_mongdb_review()
r_db = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
ld_sdk = LDApi(r'D:\leidian\LDPlayer9')  # 链接雷电模拟器驱动
ip = ChangeIP(r_db, ld_sdk.adb)
# 自定义条形码下文字
class CustomWriter(ImageWriter):
    def __init__(self, extra_text, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.extra_text = extra_text

buyers = [
    '79104684025',
    '79801811276',
    '79801813447',
    '79104980323',
    '79104981586',
    '79104981159',
    '79104981490',
    '79104981480',
    '79104981485',
    '79801812446',
    '79801814044',
    '79104073560',
    '79801813966',
    '79104981533',
    '79801814479',
    '79801815917',
    '79801811795',
    '79104981269',
    '79104981386',
    '79104981335',
    '79104683924',
    '79104934738',
    '79104684832',
    '79104934896',
    '79153391734',
    '79153390838',
    '79153379798',
    '79153379721',
    '79153379558',
    '79153379924',
    '79153380271',
    '79153379583',
    '79153379342',
    '79153379958',
    '79153380358',
    '79153391201',
    '79153379501',
    '79153379610',
    '79153380298',
    '79153391208',
    '79153390802',
    '79153390849',
    '79153390854',
    '79153391136',
    '79153379476',
    '79104934761',
    '79153379453',


]


def get_wb_code(page: Page,buyer):
    with page.expect_response(lambda response: response.url=='https://www.wildberries.ru/webapi/v2/lk/myorders/delivery/active' and response.status==200) as response_info:
        page.goto('https://www.wildberries.ru/lk/myorders/delivery', wait_until="networkidle", timeout=10000000)
    # page.goto('https://www.wildberries.ru/lk/myorders/delivery', wait_until="networkidle", timeout=10000000)
    if "login" in page.url:
        print("wb账号 {} 未登陆".format(buyer))
        return None
        page.locator('input.input-item').clear()
        page.locator('input.input-item').fill(buyer)
        page.click('button:has-text("Получить код")')
        #page.pause()
        if page.get_by_role("link", name="Перейти в корзину").count() > 0:
            print('账号:{},wb无货取'.format(buyer))
            return
    if "basket" in page.url:
        page.pause()
    if page.get_by_role("link", name="Перейти в корзину").count() > 0:
        print('账号:{},wb无货取'.format(buyer))
        return
    pickup_info = {'platform': None, 'address': None, 'pids': [], 'code': None, 'qrcodeImg': None, 'date_at': None}
    data = json.loads(response_info.value.body())
    positions = data.get('value').get('positions')
    pids = []
    for position in positions:
        if pickup_info.get('address') is None:
            pickup_info['address'] = position.get('address')
        if position.get('trackingStatus') == 'Готов к выдаче':#到达派送点
            pids.append(position.get('code1S'))
    pickup_info['pids'] = pids
    if len(pids) <= 0:
        print('账号:{},wb无货取'.format(buyer))
        return
    qrcode_base64=page.locator('.delivery-qr__code').get_attribute('src')
    pickup_info['qrcodeImg'] = str(qrcode_base64).split(',',-1)[1]
    pickup_info['date_at'] = datetime.now()
    binary_data = base64.b64decode(pickup_info['qrcodeImg'])
    pickup_info['code'] = decode(Image.open(BytesIO(binary_data)))[0].data.decode('ascii')
    pickup_info['buyer'] = buyer
    pickup_info['return_date'] = datetime.now()
    pickup_info['platform'] = 'wb'
    m_db['pickup_info'].replace_one({'platform': 'wb', 'buyer': buyer,'code':pickup_info['code']}, pickup_info, True)



def get_ozon_code(page: Page,buyer):
    page.goto('https://www.ozon.ru/my/orderlist?sort=2', wait_until="networkidle", timeout=10000000)
    body_str = page.inner_html('body')
    is_Login_text = re.findall('Вы не авторизованы', body_str)
    if len(is_Login_text) > 0:
        print('账号:{},ozon未登陆'.format(buyer))
        page.pause()
        return None
    is_not_order = re.findall('state-orderList-(.+)"', body_str)
    if len(is_not_order) == 0:
        print('账号:{},ozon无货取'.format(buyer))
        return None

    adderss_txt = re.findall(',"fullName":"(.*?)","name":"', body_str)[0]
    if page.locator('div[id^="state-receiptCode"]').count() <= 0:
        print('账号:{},ozon无货取'.format(buyer))
        return None
    temp_str = json.loads(page.locator('div[id^="state-receiptCode"]').get_attribute('data-state')).get('title').get('text')
    bar_code_txt =str(temp_str).replace(' — код для получения в пунктах выдачи и постаматах Ozon','').replace(' ','',-1)
    img = Code128(bar_code_txt, writer=CustomWriter(extra_text=buyer[:-5]))
    img.save("1233")
    with open("1233.png", "rb") as f:
        qrcodeImg = base64.b64encode(f.read()).decode('utf-8')
    os.remove("1233.png")

    pids = []
    order_locators = page.locator('div[id^="state-orderList"]').all()
    for po in order_locators:
        order_list = json.loads(po.get_attribute('data-state')).get('orderList')
        for order in order_list:
            sections = order.get('sections')
            for section in sections:
                description = section.get('description') #判断是否到达派送点
                if description is not None:
                    continue
                products = section.get('products')
                for product in products:
                    # /product/lvq-n555-noutbuk-ram-16-gb-korallovyy-1684203690/
                    #/my/orderdetails/?order=0174027228-0008
                    if product.get('link').find('/product/') > 0:
                        pids.append(re.findall(r'-(\d+)?/', product.get('link'))[0])
                    else:
                        pids.append(re.findall(r'order=(.+)?', product.get('link'))[0])



    pickup_info = {
        "platform": "ozon",
        "address": adderss_txt,
        "pids": pids,
        "code": bar_code_txt,
        "qrcodeImg": qrcodeImg,
        "date_at": datetime.now(),
        "return_date": datetime.now(),
        "buyer": buyer,
    }
    m_db['pickup_info'].replace_one({'platform': 'ozon', 'buyer': buyer,'code':bar_code_txt}, pickup_info, True)



for buyer in buyers:
    buyerInfo = BuyerInfo(buyer)
    ip.change(buyer)
    page, close_client, playwright, browser, close_env, browser_context = open_browser(buyerInfo)
    for i in range(3):  # 设置重试次数
        try:
            get_wb_code(page, buyer)
            get_ozon_code(page, buyer)
            break  # 成功运行后退出循环
        except Exception as error:
            print(f"Run failed, retrying. Error: {error}")
            if i < 2:  # 如果还有剩余尝试次数，继续尝试
                continue
            else:
                raise error  # 超过最大重试次数，抛出异常
    browser_context.close()
    browser.close()
    playwright.stop()
    time.sleep(1)
    close_env(container_code=buyerInfo.device_id)
    close_client()

