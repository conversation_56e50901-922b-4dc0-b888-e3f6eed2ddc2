import json
import os
import re
import shutil
import time
import zipfile
from datetime import datetime, timedelta
from pathlib import Path

import pandas as pd
from PIL import Image
from playwright.sync_api import sync_playwright

from common.ExchangeRate import ExchangeRate
from db.client_mongdb import login_mongdb_ozon, login_mongdb_review

try:
    import rarfile

    # 设置 WinRAR 路径
    rarfile.UNRAR_TOOL = r"D:\Program Files\WinRAR\UnRAR.exe"
    # 如果上面的路径不对，可以尝试这个路径
    if not os.path.exists(rarfile.UNRAR_TOOL):
        rarfile.UNRAR_TOOL = r"D:\Program Files (x86)\WinRAR\UnRAR.exe"
    HAS_RARFILE = True
except ImportError:
    HAS_RARFILE = False
    print("警告: rarfile 模块未安装，将无法处理 RAR 文件")


def extract_archive(archive_path, extract_path):
    """解压zip或rar文件"""
    try:
        if archive_path.endswith('.zip'):
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
        elif archive_path.endswith('.rar'):
            if HAS_RARFILE:
                try:
                    with rarfile.RarFile(archive_path, 'r') as rar_ref:
                        rar_ref.extractall(extract_path)
                except rarfile.RarCannotExec:
                    print(f"错误：无法找到 UnRAR 工具，请确保已安装 WinRAR 并设置正确的路径")
                except Exception as e:
                    print(f"处理RAR文件时出错: {archive_path}, 错误: {str(e)}")
            else:
                print(f"跳过 RAR 文件 {archive_path}，因为 rarfile 模块未安装")
    except Exception as e:
        print(f"解压文件时出错: {archive_path}, 错误: {str(e)}")


def process_excel(excel_path):
    """处理Excel文件并返回数据"""
    try:
        df = pd.read_excel(excel_path)
        data = []

        # 查找"评论1"或"评语1"的位置
        start_row = -1
        for i in range(len(df)):
            for j in range(len(df.columns)):
                cell_value = str(df.iloc[i, j]).strip()
                if cell_value == "评论1" or cell_value == "评语1":
                    start_row = i
                    break
            if start_row != -1:
                break

        if start_row == -1:
            print(f"警告: 在Excel文件中未找到'评论1'或'评语1'标记")
            return None

        # 从"评论1"或"评语1"的下一行开始读取评论内容
        comment_index = 1
        current_row = start_row

        while current_row < len(df):
            # 读取评论内容（假设评论内容在第三列，即索引为2）
            if len(df.columns) >= 3:
                comment = str(df.iloc[current_row, 2]).strip()
                if comment and comment != "nan" and comment != "/":
                    data.append({
                        f"评语{comment_index}": comment,
                        "reviewImgInput": [],
                        "reviewVideoInput": [],  # 添加视频字段
                        "sellerId": "",
                        "price": 0,
                        "status": 0
                    })
                comment_index += 1

            current_row += 1

            # 检查下一行是否是新的评论标记（如"评论2"、"评论3"或"评语2"、"评语3"等）
            if current_row < len(df):
                next_row_first_col = str(df.iloc[current_row, 0]).strip()
                is_comment_marker = (next_row_first_col.startswith("评论") or next_row_first_col.startswith(
                    "评语")) and next_row_first_col[2:].isdigit()
                if not is_comment_marker:
                    # 如果下一行是新的评论标记，但编号不连续，则结束
                    break

        return data if data else None
    except Exception as e:
        print(f"处理Excel文件出错: {excel_path}, 错误: {str(e)}")
        return None


def compress_image(input_path, output_path, max_size_mb=1):
    """压缩图片到指定大小"""
    try:
        img = Image.open(input_path)

        # 转换为RGB模式（如果是RGBA）
        if img.mode == 'RGBA':
            img = img.convert('RGB')

        quality = 95
        img.save(output_path, 'JPEG', quality=quality)

        # 如果文件大小超过1MB，逐步降低质量直到满足要求
        while os.path.getsize(output_path) > max_size_mb * 1024 * 1024 and quality > 5:
            quality -= 5
            img.save(output_path, 'JPEG', quality=quality)

        return True
    except Exception as e:
        print(f"压缩图片出错: {input_path}, 错误: {str(e)}")
        return False


# 修改处理图片和视频的部分
def process_media_file(file_path, images_dir, videos_dir, folder_num, images_by_folder, videos_by_folder):
    """处理媒体文件（图片或视频）"""
    timestamp = str(int(time.time()))
    ext = os.path.splitext(file_path)[1].lower()

    # 视频文件处理
    if ext in ['.mp4', '.avi', '.mov', '.wmv']:
        new_name = f"{timestamp}{ext}"
        dst_path = videos_dir / new_name  # 修改为 videos_dir
        shutil.copy2(str(file_path), str(dst_path))
        videos_by_folder[folder_num].append(new_name)

    # 图片文件处理
    elif ext in ['.png', '.gif', '.bmp', '.jpeg', '.jpg', '.webp']:
        new_name = f"{timestamp}.jpg"
        dst_path = images_dir / new_name

        # 如果不是jpg格式或大小超过1MB，进行转换和压缩
        if ext != '.jpg' or os.path.getsize(file_path) > 1024 * 1024:
            if compress_image(file_path, dst_path):
                images_by_folder[folder_num].append(new_name)
        else:
            shutil.copy2(str(file_path), str(dst_path))
            images_by_folder[folder_num].append(new_name)

    time.sleep(1)


# 在调用 process_media_file 的地方也需要修改

def extract_product_number(filename):
    """从文件名中提取产品编号"""
    try:
        # 使用'-'分割文件名
        parts = filename.split('-')
        # 遍历所有部分，查找符合条件的数字
        for part in parts:
            # 检查是否是纯数字且长度为9或10位
            if part.isdigit() and len(part) in [9, 10]:
                return part
        return None
    except Exception as e:
        print(f"提取产品编号出错: {filename}, 错误: {str(e)}")
        return None


# 修改collect_files函数中的相关部分
def collect_files(folder_path, scraper, user_id):
    scraper.start_browser()
    """收集所有文件的数据"""
    result = {
        "tables": []
    }

    temp_dir = Path("temp_extract")
    if temp_dir.exists():
        shutil.rmtree(temp_dir)
    temp_dir.mkdir()

    # 分别创建 images 和 videos 文件夹
    images_dir = Path(folder_path) / "images"
    videos_dir = Path(folder_path) / "videos"
    images_dir.mkdir(exist_ok=True)
    videos_dir.mkdir(exist_ok=True)

    # 遍历所有压缩文件
    for root, _, files in os.walk(folder_path):
        for file in files:
            if not file.endswith(('.zip', '.rar')):
                continue

            file_path = Path(root) / file
            print(f"处理文件: {file}")

            try:
                extract_dir = temp_dir / file.rsplit('.', 1)[0]
                extract_dir.mkdir(exist_ok=True)
                extract_archive(str(file_path), str(extract_dir))

                excel_data = None
                images_by_folder = {}
                videos_by_folder = {}  # 初始化 videos_by_folder 字典
                # 获取产品ID
                product_id = extract_product_number(file)
                # data = scraper.fetch_ozon_product(product_id)
                data = scraper.fetch_wildberries_product(product_id)
                print(data)
                # 首先处理Excel文件
                for extracted_root, _, extracted_files in os.walk(extract_dir):
                    for extracted_file in extracted_files:
                        if extracted_file.endswith(('.xlsx', '.xls')):
                            extracted_path = Path(extracted_root) / extracted_file
                            print(f"处理Excel: {extracted_file}")
                            excel_data = process_excel(str(extracted_path))
                            break
                    if excel_data:
                        break

                if not excel_data:
                    print(f"警告: 在压缩包 {file} 中未找到Excel文件")
                    continue

                # 然后处理图片和视频文件
                for extracted_root, _, extracted_files in os.walk(extract_dir):
                    current_dir = Path(extracted_root)
                    dir_name = current_dir.name

                    # 如果是数字文件夹，处理媒体文件
                    if dir_name.isdigit():
                        folder_num = int(dir_name)
                        # 确保评语序号在1-10之间
                        if 1 <= folder_num <= 10:
                            images_by_folder[folder_num] = []
                            videos_by_folder[folder_num] = []  # 为每个文件夹初始化视频列表

                            for media_file in extracted_files:
                                src_path = Path(extracted_root) / media_file
                                process_media_file(
                                    src_path,
                                    images_dir,
                                    videos_dir,
                                    folder_num,
                                    images_by_folder,
                                    videos_by_folder
                                )

                # 更新Excel数据中的媒体信息
                if excel_data:
                    current_user = scraper.get_current_user(user_id)
                    rate = scraper.get_current_rate()
                    esCost = round(data['price'] * rate / 100 * 1, 2)
                    order_base_price = current_user['info'].get('PURCHASE_ORDER_BASE_PRICE')
                    order_review_price = current_user['info'].get('PURCHASE_ORDER_REVIEW_PRICE')
                    espay = (order_review_price + order_base_price) * current_user['info'].get('FEE_DISCOUNT')
                    for i, comment_data in enumerate(excel_data):
                        # 使用时间戳后10位，并加入循环索引确保唯一性
                        timestamp = datetime.now().strftime("%m%d%H%M%S")[-8:]  # 取时间戳后8位
                        task_id = 'L' + timestamp + str(i).zfill(2)  # 补齐2位
                        comment_num = i + 1  # 由于Python索引从0开始，所以要加1
                        comment_data["reviewVideoInput"] = None
                        comment_data["reviewVideo"] = 0
                        comment_data["reviewImgInput"] = []
                        comment_data["reviewImg"] = 0
                        if comment_num in images_by_folder:
                            if len(images_by_folder[comment_num]) > 0:
                                comment_data["reviewImgInput"] = images_by_folder[comment_num]
                                comment_data["reviewImg"] = 1
                            else:
                                comment_data["reviewImgInput"] = []
                                comment_data["reviewImg"] = 0
                        if comment_num in videos_by_folder:
                            if len(videos_by_folder[comment_num]) > 0:
                                comment_data["reviewVideoInput"] = videos_by_folder[comment_num]
                                comment_data["reviewVideo"] = 1
                            else:
                                comment_data["reviewVideoInput"] = None
                                comment_data["reviewVideo"] = 0
                        if comment_data.get(f"评语{comment_num}", "") == "点五星" or comment_data.get(f"评语{comment_num}", "") == "点亮5星":
                            comment_data["reviewText"] = 0
                            comment_data["reviewTextInput"] = ""
                        else:
                            comment_data["reviewText"] = 1
                            comment_data["reviewTextInput"] = comment_data.get(f"评语{comment_num}", "")
                        comment_data["review"] = 1
                        comment_data["sellerId"] = data.get("sellerId")
                        comment_data["price"] = float(data.get("price"))
                        comment_data["status"] = data.get("status")
                        comment_data["platform"] = "wb"
                        comment_data["productId"] = int(product_id)
                        comment_data["img"] = data.get("img")
                        comment_data["preDate"] = datetime.now().replace(hour=0, minute=0, second=0,microsecond=0) + timedelta(days=i)
                        comment_data["orderPcs"] = 1
                        comment_data["keyword"] = ""
                        comment_data["like"] = 0
                        comment_data["cart"] = 0
                        comment_data["esPay"] = round(espay, 2)
                        comment_data["esCost"] = round(esCost, 2)
                        comment_data["userId"] = user_id
                        comment_data["taskId"] = task_id
                        comment_data["createTime"] = datetime.now()
                        comment_data["updateTime"] = datetime.now()
                        comment_data["orderId"] = ""
                        comment_data["buyer"] = ""
                        comment_data["remark"] = ""
                        del comment_data[f"评语{comment_num}"]

                    result["tables"].append({
                        "source_archive": file,
                        "product_id": product_id,
                        "z_price": data.get("price") * 10,
                        "data": excel_data
                    })
                    scraper.add_orders(excel_data)


            except Exception as e:
                print(f"处理压缩文件出错: {file}, 错误: {str(e)}")
                continue

    # 清理临时文件
    shutil.rmtree(temp_dir)
    scraper.browser.close()
    return result


def run_collect():
    scraper = OzonProductScraper()
    base_path = r"E:\客户评测\L0013\250422"

    result = collect_files(base_path, scraper, "L0013")

    # 保存结果到JSON文件
    output_path = Path(base_path) / "extracted_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    print(f"处理完成！数据已保存到: {output_path}")


class OzonProductScraper:
    def __init__(self):
        self.m_db = login_mongdb_ozon()
        self.r_db = login_mongdb_review()
        self.r_cache = ExchangeRate()
        self.user_data_dir = r"E:\客户评测\User Data"
        self.executable_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"

    def get_current_user(self, user_id):
        return self.r_db['userinfo'].find_one({"userId": user_id})

    def add_orders(self, orders):
        return self.r_db['orders'].insert_many(orders)
    def update_order(self, product_id, order_data):
        order_data['updateTime']=datetime.now()
        order_data["orderId"]=""
        order_data["buyer"]=""
        return self.r_db['orders'].update_many({"productId": int(product_id),'status': 0}, {"$set": order_data})

    def get_current_rate(self):
        return self.r_cache.get_rate()

    def start_browser(self):
        self.browser = sync_playwright().start().chromium.launch_persistent_context(
            headless=False,
            user_data_dir=self.user_data_dir,
            executable_path=self.executable_path,
            args=[
                '--start-maximized',
                '--disable-infobars',
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-extensions'
            ],
            ignore_default_args=['--enable-automation']
        )
        self.page = self.browser.pages[0]

    def scrape_products(self):
        products = self.m_db['t_productId_and_imgLink'].find({'stata': {'$gte': 0}})
        for product in products:
            try:
                with self.page.expect_response(re.compile(r"entrypoint-api\.bx/page/json/v2\?url=%2Fproduct%2F"),
                                               timeout=3000) as response_info:
                    self.page.goto(f"https://www.ozon.ru/product/{product['productId']}", wait_until="networkidle")
                state_dict = response_info.value.json().get('widgetStates')
                sellers = self.extract_sellers(state_dict)

                for seller in sellers:
                    if seller.get('id') == "2339636" or seller.get('id') == "2206838":
                        continue

                    new_entry = self.create_new_entry(seller, product)
                    if self.entry_exists(new_entry["removeId"]):
                        print(f"该 removeId:{new_entry['removeId']} 已存在，不插入新记录。")
                    else:
                        self.m_db['t_remove_other_listing'].insert_one(new_entry)
                        print("新记录已插入。")
            except Exception as e:
                print(f"产品 {product['productId']} 页面加载失败，跳过。错误信息: {e}")
                continue

    def extract_sellers(self, state_dict):
        sellers = []
        for key, value in state_dict.items():
            if 'webSellerList' in key:
                state_json = json.loads(value)
                sellers = state_json.get('sellers', [])
        return sellers

    def create_new_entry(self, seller, product):
        return {
            "removeId": int(seller['sku']),
            "productId": product['productId'],
            "stata": 0,
            "chatId": "",
            "createTime": datetime.now(),
            "updateTime": datetime.now()
        }

    def entry_exists(self, remove_id):
        return self.m_db['t_remove_other_listing'].find_one({"removeId": remove_id}) is not None

    def fetch_ozon_product(self, product_id):
        updatable = {"sellerId": "", "price": 0, "status": 1}
        """访问Ozon获取产品JSON数据"""
        url = f"https://www.ozon.ru/product/{product_id}"
        with self.page.expect_response(re.compile(r"entrypoint-api\.bx/page/json/v2\?url=%2Fproduct%2F"),
                                       timeout=30000) as response_info:
            self.page.goto(url, wait_until="networkidle")
        state_dict = response_info.value.json().get('widgetStates')
        self.page.pause()
        for product in state_dict:
            if isinstance(product, str):
                if 'webCurrentSeller' in product:
                    current_seller = json.loads(state_dict.get(product))
                    updatable['sellerId'] = current_seller['id']
                    image_url = json.loads(
                        self.page.locator('div[id^="state-webGallery"]').get_attribute('data-state')).get('coverImage')
                    temp_str = json.loads(
                        self.page.locator('div[id^="state-webPrice"]').get_attribute('data-state')).get('price')
                    temp_str = temp_str.replace(' ', '')
                    temp_str = temp_str.replace(' ', '')
                    temp_str = temp_str.replace('₽', '')
                    updatable['price'] = int(temp_str)
                    updatable['img'] = image_url
                    return updatable
            else:
                temp_id = product.get('id', 0)
                if temp_id == int(product_id):
                    size = product.get('sizes', [])[0]
                    seller_id = product.get('supplierId', '')
                    if size is not None:
                        price = int(size.get('price', {}).get('product', 0) // 100)
                        qty = size.get('stocks', [])[0].get('qty', 0)
                        updatable = {"sellerId": str(seller_id), "price": price, "status": 1}
                        return updatable
                        # if qty == 0:
                        #     print(f"该产品 {product_id} 已售罄。")
                        #     updatable["status"] = 0
                        #     updatable["remark"] = "商品已售罄，请添加库存"
                        #
                        # # 更新 MongoDB 中的订单状态
                        # self.r_db['orders'].update_many(
                        #     {"productId": int(product_id), 'status': 0},
                        #     {"$set": updatable}
                        # )
                        # print(f"更新成功: 产品ID={product_id}, 卖家ID={seller_id}, 价格={price}")
    def fetch_wildberries_product(self, product_id):
        """访问Wildberries获取产品JSON数据"""
        url = f"https://www.wildberries.ru/catalog/{product_id}/detail.aspx?targetUrl=SP"
        updatable = {"status": 0, "remark": ""}

        try:
            # 获取商品数据
            with self.page.expect_response(
                    lambda response: "card.wb.ru/cards/v2/detail?appType" in response.url,
                    timeout=10000
            ) as response_info:
                self.page.goto(url)

            products_dict = response_info.value.json().get('data', {}).get('products', [])
            product = next((p for p in products_dict if p.get('id') == int(product_id)), None)

            if product:
                size = next(iter(product.get('sizes', [])), None)
                seller_id = str(product.get('supplierId', ''))

                if size:
                    price = int(size.get('price', {}).get('product', 0) // 100)
                    qty = next(iter(size.get('stocks', [])), {}).get('qty', 0)

                    # 获取图片URL
                    with self.page.expect_response(
                            lambda response: f"{product_id}/images/c" in response.url,
                            timeout=10000
                    ) as img_response:
                        self.page.goto(url)

                    updatable.update({
                        "sellerId": seller_id,
                        "price": price,
                        "status": 1,
                        "img": img_response.value.url if img_response.value else ""

                    })

                    if qty == 0:
                        print(f"该产品 {product_id} 已售罄。")
                        updatable.update({
                            "status": 0,
                            "remark": "商品已售罄，请添加库存"
                        })
                    else:
                        print(f"获取成功: 产品ID={product_id}, 卖家ID={seller_id}, 价格={price}")

        except TimeoutError:
            print(f"获取产品 {product_id} 超时")
            updatable["remark"] = "获取商品信息超时"
        except Exception as e:
            print(f"获取产品 {product_id} 失败: {str(e)}")
            updatable["remark"] = f"获取商品信息异常: {str(e)}"

        return updatable

    def fetch_yandex_market_product(self, product_id):
        """访问Yandex Market获取产品JSON数据"""
        url = f"https://market.yandex.ru/pr/{product_id}"
        self.page.goto(url)
        self.page.wait_for_selector('div[data-zone-name="cpa-offer"]')
        # 处理返回的JSON数据 (可能需要更改正则表达式)
        data = self.page.locator('div[data-zone-name="cpa-offer"]').get_attribute("data-zone-data")
        json_data = json.loads(data)
        seller_id = json_data.get('businessId', '')
        price = int(json_data.get('priceDetails', {}).get('price', {}).get('value', 0))

        # 更新 MongoDB 中的订单状态
        self.r_db['orders'].update_many(
            {"productId": int(product_id), 'status': 0},
            {"$set": {"sellerId": seller_id, "price": price, "status": 1, "sellerId": seller_id, "orderId": ""}}
        )
        print(f"更新成功: 产品ID={product_id}, 卖家ID={seller_id}, 价格={price}")

    def register_mail_ru(self, phone, email, password):
        """注册Mail.ru账号"""
        self.page.goto("https://mail.ru")
    def register_ozon(self, phone,email, password):
        """注册Ozon账号"""
        self.page.goto("https://www.ozon.ru")
    def register_yandex_market(self, phone, email, password):
        """注册Yandex Market账号"""
        self.page.goto("https://market.yandex.ru")
    def register_wildberries(self, phone, email, password):
        """注册Wildberries账号"""
        self.page.goto("https://www.wildberries.ru")
    def run(self):
        self.start_browser()

        # self.scrape_products()
        # self.fetch_yandex_market_product("5830389630")
        # self.fetch_wildberries_product("325875155")
        pl = ["332657633", "332662497", ]
        for p in pl:
            po = self.fetch_wildberries_product(p)
            self.update_order(p, po)
            # self.fetch_ozon_product(p)

        input("按Enter键关闭浏览器...")
        self.browser.close()

    def register_all(self):
        self.start_browser()

        self.register_mail_ru("1234567890", "<EMAIL>", "123456")
        self.register_ozon("1234567890", "<EMAIL>", "123456")
        self.register_yandex_market("1234567890", "<EMAIL>", "123456")
        self.register_wildberries("1234567890", "<EMAIL>", "123456")
        input("按Enter键关闭浏览器...")
        self.browser.close()


if __name__ == "__main__":
    run_collect()
    # scraper = OzonProductScraper()
    # scraper.run()
