#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据拆分处理程序
按区域拆分SKU库存数据，生成符合指定格式的区域文件

作者: AI Assistant
日期: 2025-08-22
版本: 2.0 - 移除参考格式文件依赖
"""

import pandas as pd
import openpyxl
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import Font, Alignment, Border, Side
import os
import sys
import re
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_splitter.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ExcelDataSplitter:
    """Excel数据拆分处理器"""

    def __init__(self, main_file_path: str, output_dir: str = "output"):
        """
        初始化数据拆分器

        Args:
            main_file_path (str): 主数据文件路径
            output_dir (str): 输出目录
        """
        self.main_file_path = main_file_path
        self.output_dir = output_dir

        # 区域映射字典 (俄文列名 -> 中文名称)
        self.region_mapping = {
            'Москва, МО и Дальние регионы\n01': '莫斯科、莫斯科州和偏远地区',
            'Санкт-Петербург и СЗО\n02': '圣彼得堡和西北分部',
            'Юг\n03': '南部',
            'Урал\n04': '乌拉尔',
            'Казань\n05': '喀山',
            'Ярославль\n06': '雅罗斯拉夫尔',
            'Саратов\n07': '萨拉托夫',
            'Сибирь\n08': '西伯利亚',
            'Уфа\n09': '乌法',
            'Казахстан\n10': '哈萨克斯坦',
            'Воронеж\n11': '沃罗涅日',
            'Кавказ\n12': '高加索',
            'Тюмень\n13': '秋明',
            'Дальний Восток\n14': '远东',
            'Самара\n15': '萨马拉',
            'Армения\n16': '亚美尼亚',
            'Калининград\n17': '加里宁格勒',
            'Беларусь\n18': '白俄罗斯',
            'Красноярск\n19': '克拉斯诺亚尔斯克',
            'Кыргызстан\n20': '吉尔吉斯斯坦'
        }

        # 区域英文名称映射 (用于文件命名)
        self.region_english_names = {
            'Москва, МО и Дальние регионы\n01': 'Moscow',
            'Санкт-Петербург и СЗО\n02': 'Saint-Petersburg',
            'Юг\n03': 'South',
            'Урал\n04': 'Ural',
            'Казань\n05': 'Kazan',
            'Ярославль\n06': 'Yaroslavl',
            'Саратов\n07': 'Saratov',
            'Сибирь\n08': 'Siberia',
            'Уфа\n09': 'Ufa',
            'Казахстан\n10': 'Kazakhstan',
            'Воронеж\n11': 'Voronezh',
            'Кавказ\n12': 'Caucasus',
            'Тюмень\n13': 'Tyumen',
            'Дальний Восток\n14': 'Far-East',
            'Самара\n15': 'Samara',
            'Армения\n16': 'Armenia',
            'Калининград\n17': 'Kaliningrad',
            'Беларусь\n18': 'Belarus',
            'Красноярск\n19': 'Krasnoyarsk',
            'Кыргызстан\n20': 'Kyrgyzstan'
        }

        self.main_data = None
        self.processing_report = {
            'total_regions': 0,
            'processed_regions': 0,
            'total_skus': 0,
            'errors': [],
            'output_files': []
        }

        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)

    def load_data(self) -> bool:
        """
        加载主数据文件

        Returns:
            bool: 加载是否成功
        """
        try:
            logger.info("开始加载数据文件...")

            # 加载主数据文件
            if not os.path.exists(self.main_file_path):
                raise FileNotFoundError(f"主数据文件不存在: {self.main_file_path}")

            self.main_data = pd.read_excel(self.main_file_path, sheet_name=0)
            logger.info(f"主数据文件加载成功: {self.main_data.shape[0]} 行 x {self.main_data.shape[1]} 列")

            return True

        except Exception as e:
            error_msg = f"加载数据文件时出错: {e}"
            logger.error(error_msg)
            self.processing_report['errors'].append(error_msg)
            return False

    def clean_and_validate_data(self) -> bool:
        """
        清理和验证数据

        Returns:
            bool: 验证是否成功
        """
        try:
            logger.info("开始清理和验证数据...")

            # 清理主数据 - 跳过前两行标题行，从第3行开始是实际数据
            if len(self.main_data) < 3:
                raise ValueError("主数据文件行数不足，无法处理")

            # 获取实际数据行（跳过标题行）
            self.main_data = self.main_data.iloc[2:].copy()

            # 重置索引
            self.main_data.reset_index(drop=True, inplace=True)

            # 验证必要的列是否存在
            required_columns = ['产品型号']
            for col in required_columns:
                if col not in self.main_data.columns:
                    raise ValueError(f"主数据文件缺少必要列: {col}")

            # 移除空的SKU行
            self.main_data = self.main_data.dropna(subset=['产品型号'])

            # 验证区域列是否存在
            available_regions = []
            for region_col in self.region_mapping.keys():
                if region_col in self.main_data.columns:
                    available_regions.append(region_col)

            if not available_regions:
                raise ValueError("主数据文件中未找到任何区域列")

            logger.info(f"找到 {len(available_regions)} 个区域列")
            logger.info(f"清理后的数据: {len(self.main_data)} 个SKU")

            self.processing_report['total_regions'] = len(available_regions)
            self.processing_report['total_skus'] = len(self.main_data)

            return True

        except Exception as e:
            error_msg = f"清理和验证数据时出错: {e}"
            logger.error(error_msg)
            self.processing_report['errors'].append(error_msg)
            return False

    def extract_region_data(self, region_column: str) -> pd.DataFrame:
        """
        提取指定区域的数据

        Args:
            region_column (str): 区域列名

        Returns:
            pd.DataFrame: 区域数据
        """
        try:
            # 创建区域数据DataFrame
            region_data = pd.DataFrame()

            # 提取SKU和数量
            region_data['артикул'] = self.main_data['产品型号']
            region_data['имя (необязательно)'] = None  # 名称列保持为空

            # 提取区域数量，处理可能的NaN值
            quantities = self.main_data[region_column].fillna(0)

            # 确保数量为数值类型
            region_data['количество'] = pd.to_numeric(quantities, errors='coerce').fillna(0).astype(int)

            # 过滤掉数量为0的记录
            region_data = region_data[region_data['количество'] > 0]

            # 重置索引
            region_data.reset_index(drop=True, inplace=True)

            return region_data

        except Exception as e:
            logger.error(f"提取区域 {region_column} 数据时出错: {e}")
            return pd.DataFrame()

    def create_region_file(self, region_data: pd.DataFrame, region_column: str, region_name: str, date_str: str) -> str:
        """
        创建区域Excel文件

        Args:
            region_data (pd.DataFrame): 区域数据
            region_column (str): 区域列名
            region_name (str): 区域英文名称
            date_str (str): 日期字符串

        Returns:
            str: 输出文件路径
        """
        try:
            # 获取中文名称
            chinese_name = self.region_mapping.get(region_column, region_name)

            # 生成文件名 (格式: 英文名-中文名-日期.xlsx)
            filename = f"{region_name}-{chinese_name}-{date_str}.xlsx"
            output_path = os.path.join(self.output_dir, filename)

            # 创建工作簿
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "Sheet1 (2)"  # 标准工作表名称

            # 写入数据
            for r in dataframe_to_rows(region_data, index=False, header=True):
                worksheet.append(r)

            # 设置列宽
            worksheet.column_dimensions['A'].width = 15  # артикул
            worksheet.column_dimensions['B'].width = 20  # имя (необязательно)
            worksheet.column_dimensions['C'].width = 12  # количество

            # 设置标题行格式
            for cell in worksheet[1]:
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

            # 保存文件
            workbook.save(output_path)
            workbook.close()

            logger.info(f"成功创建区域文件: {output_path} ({len(region_data)} 个SKU)")
            return output_path

        except Exception as e:
            error_msg = f"创建区域文件时出错: {e}"
            logger.error(error_msg)
            self.processing_report['errors'].append(error_msg)
            return ""

    def validate_output_file(self, file_path: str) -> bool:
        """
        验证输出文件的格式和数据完整性

        Args:
            file_path (str): 输出文件路径

        Returns:
            bool: 验证是否通过
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"输出文件不存在: {file_path}")
                return False

            # 读取生成的文件
            df = pd.read_excel(file_path)

            # 验证列名
            expected_columns = ['артикул', 'имя (необязательно)', 'количество']
            if list(df.columns) != expected_columns:
                logger.error(f"文件 {file_path} 列名不匹配，期望: {expected_columns}, 实际: {list(df.columns)}")
                return False

            # 验证数据类型
            if not df['артикул'].dtype == 'object':
                logger.error(f"文件 {file_path} 中 артикул 列数据类型错误")
                return False

            if not pd.api.types.is_numeric_dtype(df['количество']):
                logger.error(f"文件 {file_path} 中 количество 列数据类型错误")
                return False

            # 验证数据完整性
            if df['артикул'].isnull().any():
                logger.error(f"文件 {file_path} 中存在空的SKU")
                return False

            if (df['количество'] <= 0).any():
                logger.error(f"文件 {file_path} 中存在非正数数量")
                return False

            logger.info(f"文件验证通过: {file_path}")
            return True

        except Exception as e:
            logger.error(f"验证文件 {file_path} 时出错: {e}")
            return False

    def process_all_regions(self, date_str: str = None) -> bool:
        """
        处理所有区域数据

        Args:
            date_str (str): 日期字符串，如果为None则使用当前日期

        Returns:
            bool: 处理是否成功
        """
        try:
            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")

            logger.info(f"开始处理所有区域数据，日期: {date_str}")

            processed_count = 0

            # 遍历所有区域
            for region_col, chinese_name in self.region_mapping.items():
                if region_col not in self.main_data.columns:
                    logger.warning(f"跳过不存在的区域列: {region_col}")
                    continue

                logger.info(f"处理区域: {chinese_name} ({region_col})")

                # 提取区域数据
                region_data = self.extract_region_data(region_col)

                if len(region_data) == 0:
                    logger.info(f"区域 {chinese_name} 无有效数据，跳过")
                    continue

                # 获取英文名称
                english_name = self.region_english_names.get(region_col, "Unknown")

                # 创建区域文件
                output_path = self.create_region_file(region_data, region_col, english_name, date_str)

                if output_path:
                    # 验证输出文件
                    if self.validate_output_file(output_path):
                        self.processing_report['output_files'].append({
                            'region': chinese_name,
                            'file_path': output_path,
                            'sku_count': len(region_data)
                        })
                        processed_count += 1
                    else:
                        logger.error(f"区域文件验证失败: {output_path}")
                        self.processing_report['errors'].append(f"区域文件验证失败: {output_path}")

            self.processing_report['processed_regions'] = processed_count
            logger.info(f"处理完成，成功处理 {processed_count} 个区域")

            return processed_count > 0

        except Exception as e:
            error_msg = f"处理所有区域时出错: {e}"
            logger.error(error_msg)
            self.processing_report['errors'].append(error_msg)
            return False

    def generate_report(self) -> str:
        """
        生成处理报告

        Returns:
            str: 报告内容
        """
        report = []
        report.append("=" * 60)
        report.append("Excel数据拆分处理报告")
        report.append("=" * 60)
        report.append(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"主数据文件: {self.main_file_path}")
        report.append(f"输出目录: {self.output_dir}")
        report.append("")

        report.append("处理统计:")
        report.append(f"  总区域数: {self.processing_report['total_regions']}")
        report.append(f"  已处理区域数: {self.processing_report['processed_regions']}")
        report.append(f"  总SKU数: {self.processing_report['total_skus']}")
        report.append("")

        if self.processing_report['output_files']:
            report.append("输出文件:")
            for file_info in self.processing_report['output_files']:
                report.append(f"  {file_info['region']}: {file_info['file_path']} ({file_info['sku_count']} SKUs)")

        if self.processing_report['errors']:
            report.append("")
            report.append("错误信息:")
            for error in self.processing_report['errors']:
                report.append(f"  - {error}")

        report.append("")
        report.append("=" * 60)

        return "\n".join(report)

    def run(self, date_str: str = None) -> bool:
        """
        运行完整的数据拆分流程

        Args:
            date_str (str): 日期字符串，格式为YYYYMMDD

        Returns:
            bool: 处理是否成功
        """
        try:
            logger.info("开始Excel数据拆分处理...")

            # 1. 加载数据
            if not self.load_data():
                return False

            # 2. 清理和验证数据
            if not self.clean_and_validate_data():
                return False

            # 3. 处理所有区域
            if not self.process_all_regions(date_str):
                return False

            # 4. 生成报告
            report = self.generate_report()
            print(report)

            # 保存报告到文件
            report_file = os.path.join(self.output_dir, f"processing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)

            logger.info(f"处理报告已保存到: {report_file}")
            logger.info("Excel数据拆分处理完成!")

            return True

        except Exception as e:
            error_msg = f"运行数据拆分流程时出错: {e}"
            logger.error(error_msg)
            self.processing_report['errors'].append(error_msg)
            return False


def main():
    """主函数"""
    print("Excel数据拆分处理程序")
    print("=" * 50)

    # 配置文件路径
    main_file = "file/20250825库存情况和分配计划-对外.xlsx"
    output_dir = "output"

    # 日期字符串
    date_str = "20250825"  # 可以从文件名中自动提取

    try:
        # 创建数据拆分器
        splitter = ExcelDataSplitter(main_file, output_dir)

        # 运行处理流程
        success = splitter.run(date_str)

        if success:
            print("\n✅ 数据拆分处理成功完成!")
            print(f"📁 输出文件保存在: {output_dir}")
        else:
            print("\n❌ 数据拆分处理失败!")
            print("请查看日志文件获取详细错误信息")

    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        logger.error(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()