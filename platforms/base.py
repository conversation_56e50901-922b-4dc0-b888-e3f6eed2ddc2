from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime
from playwright.sync_api import Page
import logging
from retrying import retry

class BasePlatform(ABC):
    def __init__(self, db, redis_client):
        self.db = db
        self.redis = redis_client
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def save_operate_key(self, buyer: str, key: str) -> None:
        """保存操作记录"""
        seconds_left = self._get_seconds_until_tomorrow()
        if buyer == key:
            self.redis.set(f"{buyer}", 1, ex=seconds_left)
        else:
            self.redis.set(f"{buyer}_{key}", 1, ex=seconds_left)
            
    def _get_seconds_until_tomorrow(self) -> int:
        """获取到明天0点的秒数"""
        from datetime import datetime, time, timedelta
        tomorrow = datetime.combine(datetime.now().date() + timedelta(days=1), time(0, 0, 0))
        return int((tomorrow - datetime.now()).total_seconds())
        
    @retry(stop_max_attempt_number=3, wait_fixed=10000)
    @abstractmethod
    def check_login(self, page: Page, buyer: str) -> bool:
        """检查登录状态"""
        pass
        
    @retry(stop_max_attempt_number=3, wait_fixed=10000)
    @abstractmethod
    def get_pickup_code(self, page: Page, buyer: str) -> Optional[Dict[str, Any]]:
        """获取取货码"""
        pass
        
    @retry(stop_max_attempt_number=3, wait_fixed=10000)
    @abstractmethod
    def check_order_status(self, page: Page, buyer: str) -> List[Dict[str, Any]]:
        """检查订单状态"""
        pass
        
    @retry(stop_max_attempt_number=3, wait_fixed=10000)
    @abstractmethod
    def review_order(self, page: Page, buyer: str, review_items: List[Dict[str, Any]]) -> bool:
        """评论订单"""
        pass
        
    @retry(stop_max_attempt_number=3, wait_fixed=10000)
    @abstractmethod
    def buy_items(self, page: Page, buyer: str, items: List[Dict[str, Any]]) -> Optional[str]:
        """购买商品"""
        pass 