from typing import List, Optional, Dict, Any
from datetime import datetime
from playwright.sync_api import Page
import json
import re
import random
from .base import BasePlatform
from dingding_robt import DingTalkRobot, MessageBuilder

class WildberriesPlatform(BasePlatform):
    def __init__(self, db, redis_client):
        super().__init__(db, redis_client)
        self.robot = DingTalkRobot()
        
    def check_login(self, page: Page, buyer: str) -> bool:
        """检查WB登录状态"""
        page.goto('https://www.wildberries.ru', wait_until="networkidle", timeout=10000000)
        page.wait_for_timeout(1_000)
        if page.get_by_text('Войти').count() > 0:
            self.logger.info(f'账号:{buyer} wb未登录')
            self.robot.send(
                message_type="text",
                content=MessageBuilder.text(f"账号:{buyer} WB错误 cookies 无法加载"),
                at_all=False
            )
            page.pause()
            return False
        return True
        
    def get_pickup_code(self, page: Page, buyer: str) -> Optional[Dict[str, Any]]:
        """获取WB取货码"""
        # 检查今天是否已获取
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        if self.db['pickup_info'].find_one({
            'platform': 'wb', 
            'buyer': buyer, 
            'date_at': {'$gte': today}
        }) is not None:
            self.logger.info(f'账号:{buyer},wb今天已经获取过取件码')
            return None
            
        pickup_info = {
            'platform': 'wb',
            'address': None,
            'pids': [],
            'code': None,
            'qrcodeImg': None,
            'date_at': None,
            'return_date': None
        }
        
        # 获取订单列表
        with page.expect_response(re.compile(r'wbxoofex\.wildberries\.ru/api/v2/orders')) as response_info:
            page.goto('https://www.wildberries.ru/lk/myorders/delivery')
            page.wait_for_timeout(5000)
            page.wait_for_load_state('networkidle')
            
        data_dicts = response_info.value.json().get('data', [])
        for data_dict in data_dicts:
            for rid in data_dict.get('rids'):
                expiry_dt = rid.get('expiry_dt', 0)
                if expiry_dt > 0:
                    pickup_info['pids'].append(rid.get('nm_id'))
                    pickup_info['return_date'] = datetime.fromtimestamp(expiry_dt)
                    
        if not pickup_info['pids']:
            self.logger.info(f'账号:{buyer},wb无货取')
            return None
            
        # 获取取货码和地址
        pickup_info['address'] = page.locator('.delivery-address__info').first.text_content().strip()
        qrcode_base64 = page.locator('.delivery-qr__code').get_attribute('src')
        pickup_info['qrcodeImg'] = str(qrcode_base64).split(',', -1)[1]
        pickup_info['date_at'] = datetime.now()
        
        # 解码取货码
        from PIL import Image
        from io import BytesIO
        from pyzbar.pyzbar import decode
        import base64
        
        binary_data = base64.b64decode(pickup_info['qrcodeImg'])
        pickup_info['code'] = decode(Image.open(BytesIO(binary_data)))[0].data.decode('ascii')
        pickup_info['buyer'] = buyer
        
        # 保存到数据库
        self.db['pickup_info'].replace_one(
            {
                'platform': 'wb',
                'buyer': buyer,
                'code': pickup_info['code'],
                'address': pickup_info['address']
            },
            pickup_info,
            True
        )
        
        return pickup_info
        
    def check_order_status(self, page: Page, buyer: str) -> List[Dict[str, Any]]:
        """检查WB订单状态"""
        with page.expect_response(re.compile(r'myorders/goods/notevaluated')) as response_info:
            page.goto('https://www.wildberries.ru/lk/myorders/delivery')
            
        self.logger.info('进入wildberries订单列表页')
        wb_wait_review_items = response_info.value.json().get('value', [])
        
        if not wb_wait_review_items:
            ors_result = self.db['orders'].find({
                'buyer': buyer,
                'platform': 'wb',
                'status': {'$in': [3, 4]}
            })
            self.logger.info('没订单可评论')
            for order in ors_result:
                if order.get('status') == 3:
                    self.logger.info(f'{buyer} 订单:{order["taskId"]} 产品:{order["productId"]} 未收货')
            return []
            
        # 处理待评论订单
        for i, wait_review_item in enumerate(wb_wait_review_items):
            pid = int(wait_review_item['code1S'])
            order = self.db['orders'].find_one({'buyer': buyer, 'productId': pid}, {'_id': 0})
            
            if not order:
                self.logger.info(f'{buyer} 产品:{pid} 找不到订单')
                continue
                
            wb_wait_review_items[i]['order'] = order
            
            # 更新订单状态
            match order.get('status'):
                case 3:
                    self.db['orders'].update_one(
                        {'buyer': buyer, 'productId': pid},
                        {'$set': {'status': 4, 'updateTime': datetime.now()}}
                    )
                    self.logger.info(f'{buyer} 订单:{order["taskId"]} 产品:{pid} 标记已取货')
                case 4:
                    self.logger.info(f'{buyer} 订单:{order["taskId"]} 产品:{pid} 已收货')
                case 5:
                    self.logger.info(f'{buyer} 订单:{order["taskId"]} 产品:{pid} 已评论')
                case -1:
                    self.logger.info(f'{buyer} 订单:{order["taskId"]} 产品:{pid} 已完成')
                    
        return wb_wait_review_items
        
    def review_order(self, page: Page, buyer: str, review_items: List[Dict[str, Any]]) -> bool:
        """评论WB订单"""
        if not review_items:
            return False
            
        self.logger.info(f'共有{len(review_items)}个待评论订单')
        
        for item in review_items:
            order = item.get('order')
            if not order:
                continue
                
            with page.expect_response(
                lambda response: f'goodsArchive?nmId={item["code1S"]}' in response.url
            ) as response_info:
                self.logger.info(f'点击-评论产品:{item["code1S"]} 按钮')
                product_loc = page.locator(f'img[src*="{item["code1S"]}"]').locator('..')
                product_loc.locator('ul.delivery-rate__rate-star li[data-rating="5"]').click()
                page.wait_for_timeout(1_000)
                
            if response_info.value.status == 200:
                self._handle_review_submission(page, order, buyer)
                
        return True
        
    def _handle_review_submission(self, page: Page, order: Dict[str, Any], buyer: str) -> None:
        """处理评论提交"""
        self.logger.info(f'打开-评论产品:{order["productId"]} 评论页面')
        page.wait_for_timeout(1_000)
        
        # 点击五星
        page.locator('.popup-review__rate-wrap ul.rate-star>li').first.click()
        
        # 填写评论
        review_text = order.get('reviewTextInput', '')
        if review_text:
            page.locator('#commentTextarea').type(review_text, timeout=300000, delay=113)
            
        # 处理视频上传
        self._handle_video_upload(page, order)
        
        # 处理图片上传
        self._handle_image_upload(page, order)
        
        # 提交评论
        page.click('button:has-text("Отправить отзыв")')
        page.wait_for_timeout(1_000)
        
        if page.get_by_role("button", name="Отправить как есть").is_visible():
            page.click('button:has-text("Отправить как есть")')
            page.wait_for_timeout(1_000)
            
        page.get_by_role("button", name="Хорошо").click()
        page.wait_for_timeout(1_000)
        
        # 更新订单状态
        self.db['orders'].update_one(
            {'taskId': order['taskId']},
            {'$set': {'status': 5, 'updateTime': datetime.now()}}
        )
        
    def _handle_video_upload(self, page: Page, order: Dict[str, Any]) -> None:
        """处理视频上传"""
        video_name = order.get('reviewVideoInput')
        if not video_name or video_name == "":
            return
            
        import requests
        import mimetypes
        
        mime_type, _ = mimetypes.guess_type(video_name)
        video_resp = requests.get(f'https://www.svetx.com/video/{video_name}')
        
        if video_resp.status_code == 200:
            self.logger.info(f'上传评论视频:{video_name}')
            data_files = [{
                "name": video_name,
                "mimeType": mime_type,
                "buffer": video_resp.content
            }]
            page.locator('#video-load').set_input_files(files=data_files, timeout=600_000)
            self.logger.info(f'完成评论视频上传:{video_name}')
            
            if page.locator('text="Видео длится больше 2 минут, загрузите другое"').count() > 0:
                self.logger.info('视频长度过长，请修改小于2分钟')
                self.db['orders'].update_one(
                    {'taskId': order['taskId']},
                    {'$set': {'remark': '视频长度过长，请修改小于2分钟'}}
                )
                return
                
            page.pause()
        else:
            self.logger.info(f'评论视频 404错误:{video_name},上传失败')
            self.db['orders'].update_one(
                {'taskId': order['taskId']},
                {'$set': {'remark': f'{video_name} 视频无法链接'}}
            )
            
    def _handle_image_upload(self, page: Page, order: Dict[str, Any]) -> None:
        """处理图片上传"""
        e_input_img = page.locator('input[id="img-load"]')
        review_img_list = order.get('reviewImgInput', [])
        
        if not review_img_list:
            return
            
        self.logger.info(f'评论图片张数:{len(review_img_list)}')
        
        import requests
        import mimetypes
        import time
        
        for img_name in review_img_list:
            mime_type, _ = mimetypes.guess_type(img_name)
            img_resp = requests.get(f'https://www.svetx.com/img/{img_name}')
            
            if img_resp.status_code == 200:
                self.logger.info(f'上传评论图片:{img_name}')
                data_files = [{
                    "name": img_name,
                    "mimeType": mime_type,
                    "buffer": img_resp.content
                }]
                e_input_img.set_input_files(files=data_files, timeout=120_000)
                self.logger.info(f'完成评论图片上传:{img_name}')
                
                if page.locator('text="Фото не подходит: нужно не меньше 337 px по ширине и 450 px по высоте"').count() > 0:
                    self.logger.info('图片尺寸过小')
                    self.db['orders'].update_one(
                        {'taskId': order['taskId']},
                        {'$set': {'remark': '图片尺寸过小'}}
                    )
                    continue
                    
                if page.locator('text="Не получилось загрузить, попробуйте еще раз"').count() > 0:
                    self.logger.info('上传失败,跳过')
                    self.db['orders'].update_one(
                        {'taskId': order['taskId']},
                        {'$set': {'remark': '上传失败'}}
                    )
                    continue
                    
                page.wait_for_timeout(5_000)
            else:
                self.logger.info(f'评论图片 404错误:{img_name},上传失败')
                self.db['orders'].update_one(
                    {'taskId': order['taskId']},
                    {'$set': {'remark': f'{img_name} 图片无法链接'}}
                )
                page.pause()
                continue
                
    def buy_items(self, page: Page, buyer: str, items: List[Dict[str, Any]]) -> Optional[str]:
        """购买WB商品"""
        if not items:
            return None
            
        new_item_list = []
        r_cart_price = 0
        budget_amount = 0
        
        page.goto('https://www.wildberries.ru')
        page.wait_for_load_state('networkidle')
        
        for item in items:
            budget_amount += item['price']
            
            if item.get('keyword'):
                self._handle_keyword_search(page, item, buyer, new_item_list, r_cart_price)
            else:
                self._handle_product_id_search(page, item, buyer, new_item_list, r_cart_price)
                
        if not new_item_list:
            return None
            
        # 处理购物车和支付
        return self._handle_cart_and_payment(page, buyer, new_item_list, budget_amount, r_cart_price)
        
    def _handle_keyword_search(self, page: Page, item: Dict[str, Any], buyer: str,
                             new_item_list: List[Dict[str, Any]], r_cart_price: float) -> None:
        """处理关键词搜索"""
        page.get_by_role("searchbox", name="Найти на Wildberries").type(
            item.get('keyword', ''), delay=217
        )
        page.wait_for_timeout(1000)
        
        page_num = 1
        while True:
            if page_num >= 7:
                item['keyword'] = ""
                product_id = str(item['productId'])
                break
                
            with page.expect_response(
                lambda response: re.compile(r'search\.wb\.ru/exactmatch/ru').search(response.url)
            ) as resp_info:
                if page_num == 1:
                    page.get_by_label("Поиск товара").click()
                    page_num += 1
                else:
                    self.logger.info(f'商品:{item["productId"]} 没有找到-翻页{page_num}')
                    if page.get_by_role("link", name=f"{page_num}", exact=True).count != 0:
                        if page.get_by_role("link", name=f"{page_num}", exact=True).is_visible():
                            page.get_by_role("link", name=f"{page_num}", exact=True).click()
                            page_num += 1
                            
                page.wait_for_timeout(8000)
                page.wait_for_load_state("networkidle")
                
            search_temp = resp_info.value.json().get('data')
            if not search_temp:
                continue
                
            if search_temp.get('total', 0) == 0:
                self.logger.info(f'商品:{item["productId"]} 没有找到')
                break
            elif search_temp.get('total', 0) == 1:
                self._add_single_product_to_cart(page, item, buyer, new_item_list, r_cart_price)
                break
            else:
                self._add_product_from_list(page, item, buyer, new_item_list, r_cart_price, search_temp)
                break
                
    def _add_single_product_to_cart(self, page: Page, item: Dict[str, Any], buyer: str,
                                  new_item_list: List[Dict[str, Any]], r_cart_price: float) -> None:
        """添加单个商品到购物车"""
        cart_price = search_temp.get('products', [])[0]['sizes'][0]['price']['product'] * 0.01
        r_cart_price += cart_price
        if cart_price > item['price']:
            item['price'] = cart_price
        page.locator(f"#c{item['productId']} .product-card__order-wrap a").click()
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)
        new_item_list.append(item)
        page.get_by_label("Очистить поиск").click()
        page.wait_for_timeout(800)
        
    def _add_product_from_list(self, page: Page, item: Dict[str, Any], buyer: str,
                             new_item_list: List[Dict[str, Any]], r_cart_price: float,
                             search_temp: Dict[str, Any]) -> None:
        """从商品列表中添加商品到购物车"""
        search_items = search_temp.get('products', [])
        for index, search_item in enumerate(search_items):
            if index in [0, 9, 18, 27, 36, 45, 54, 63, 72, 81]:
                page.wait_for_timeout(1320)
                page.evaluate("window.scrollBy(0, window.innerHeight)")
                
            if search_item['id'] == int(item["productId"]):
                cart_price = search_items[index]['sizes'][0]['price']['product'] * 0.01
                r_cart_price += cart_price
                if cart_price > item['price']:
                    item['price'] = cart_price
                    
                page.locator(f"#c{search_item['id']}").scroll_into_view_if_needed()
                page.locator(f"#c{search_item['id']} .product-card__order-wrap a").first.click()
                page.wait_for_timeout(1020)
                
                self.logger.info(f'{buyer} 商品:{item["productId"]} 加入购物车 单价:{cart_price}₽')
                page.get_by_label("Очистить поиск").click()
                page.wait_for_timeout(800)
                new_item_list.append(item)
                break
                
    def _handle_product_id_search(self, page: Page, item: Dict[str, Any], buyer: str,
                                new_item_list: List[Dict[str, Any]], r_cart_price: float) -> None:
        """处理商品ID搜索"""
        page_num = 8
        product_id = str(item['productId'])
        
        if page.get_by_label("Очистить поиск").is_visible():
            page.get_by_label("Очистить поиск").click()
            page.wait_for_timeout(700)
            
        if page_num > 7 and product_id:
            with page.expect_response(
                lambda response: re.compile(r'card\.wb\.ru/cards').search(response.url)
            ) as resp_info:
                page.get_by_role("searchbox", name="Найти на Wildberries").type(product_id, delay=217)
                page.get_by_label("Поиск товара").click()
                
            page.wait_for_timeout(8000)
            page.wait_for_load_state("networkidle")
            
            search_temp = resp_info.value.json().get('data')
            if search_temp:
                search_items = search_temp.get('products', [])
                if len(search_items) == 1:
                    if search_items[0].get('totalQuantity', 0) <= 0:
                        self.logger.info(f'商品:{item["productId"]} 库存不足')
                        return
                        
                    cart_price = search_items[0]['sizes'][0]['price']['product'] * 0.01
                    r_cart_price += cart_price
                    if cart_price > item['price']:
                        item['price'] = cart_price
                        
                    page.get_by_role("button", name="Добавить в корзину").click()
                    page.wait_for_load_state("networkidle")
                    page.wait_for_timeout(2000)
                    
                    self.logger.info(f'{buyer} 商品:{product_id} 加入购物车 单价:{cart_price}₽')
                    new_item_list.append(item)
                else:
                    self.logger.info(f'商品:{item["productId"]} 没有找到')
                    
    def _handle_cart_and_payment(self, page: Page, buyer: str, new_item_list: List[Dict[str, Any]],
                               budget_amount: float, r_cart_price: float) -> Optional[str]:
        """处理购物车和支付"""
        page.get_by_role("link", name="Корзина").click()
        
        self.robot.send(
            message_type="text",
            content=MessageBuilder.text(
                f'{buyer} 购物车中共有{len(new_item_list)}件商品, 预估金额:{budget_amount}₽'
            ),
            at_all=False
        )
        
        page.pause()
        
        with page.expect_request(
            lambda request: re.compile(r'wbxoofex\.wildberries\.ru/api/v9/order\?sticker=').search(request.url),
            timeout=3000_000
        ) as req_info:
            page.get_by_role("button", name="Заказать").click()
            page.wait_for_timeout(random.randint(5, 15) * 1000)
            
        json_order = req_info.value.post_data_json.get('order', {})
        order_id = json_order.get("order_uid", "")
        order_amount = json_order.get("payment", {}).get("amount", 0) * 0.01
        payment_type_name = json_order.get("payment", {}).get("paymentType", {}).get("selectedBankCard", {}).get('name', "")
        bank_card_id = json_order.get("payment", {}).get("paymentType", {}).get("bankCardId", "")
        
        self.logger.info(f'{buyer} 订单号:{order_id} 支付方式:{payment_type_name} 银行卡:{bank_card_id}')
        self.logger.info(f'{buyer} 订单号:{order_id} 下单数:{len(new_item_list)}')
        self.logger.info(f'预估金额:{budget_amount}₽ ,订单金额:{r_cart_price}₽ ,付款金额:{order_amount}₽')
        
        for item in new_item_list:
            price_o = item['price']
            if not ("SberPay" in payment_type_name):
                price_f = int(item['price'] + item['price'] * 0.025)
            else:
                price_f = item['price']
                
            self.logger.info(f'任务:{item["taskId"]}, 原价格:{price_o}₽, 修正价格:{price_f}₽')
            item['price'] = price_f
            
            self.db['orders'].update_one(
                {'taskId': item['taskId']},
                {
                    '$set': {
                        'status': 3,
                        'orderId': order_id,
                        'buyer': buyer,
                        'price': item['price'],
                        'updateTime': datetime.now()
                    }
                }
            )
            
        with page.expect_response(re.compile(r'wbxoofex\.wildberries\.ru/api/v2/orders')) as response_info:
            page.get_by_role("link", name="Перейти в доставки").click()
            page.wait_for_timeout(3_000)
            page.wait_for_load_state('networkidle')
            
        data_dicts = response_info.value.json().get('data', [])
        is_not_pay = False
        
        for data_dict in data_dicts:
            for rid in data_dict.get('rids'):
                if rid['pay_state'] == 1:
                    is_not_pay = True
                    break
                    
        if is_not_pay:
            self._handle_payment_failure(page, bank_card_id)
            
        return order_id
        
    def _handle_payment_failure(self, page: Page, bank_card_id: str) -> None:
        """处理支付失败"""
        self.logger.info('有支付失败商品')
        page.get_by_role("button", name="К оплате").click()
        page.wait_for_timeout(3_000)
        page.wait_for_load_state('networkidle')
        page.locator(f'input[value="{bank_card_id}"]').first.locator('..').click()
        page.wait_for_timeout(800)
        page.locator("button.popup__btn-main").click()
        page.wait_for_timeout(5_000) 