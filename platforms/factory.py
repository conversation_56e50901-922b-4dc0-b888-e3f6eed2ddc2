from typing import Dict, Type
from .base import BasePlatform
from .wildberries import WildberriesPlatform
from .ozon import OzonPlatform
from .yandex import YandexPlatform

class PlatformFactory:
    _platforms: Dict[str, Type[BasePlatform]] = {
        'wb': WildberriesPlatform,
        'ozon': OzonPlatform,
        'yandex': YandexPlatform
    }
    
    @classmethod
    def create_platform(cls, platform_name: str, db, redis_client) -> BasePlatform:
        """创建平台实例"""
        platform_class = cls._platforms.get(platform_name)
        if not platform_class:
            raise ValueError(f"不支持的平台: {platform_name}")
        return platform_class(db, redis_client)
        
    @classmethod
    def register_platform(cls, name: str, platform_class: Type[BasePlatform]) -> None:
        """注册新平台"""
        cls._platforms[name] = platform_class 