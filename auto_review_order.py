
# coding:utf-8
import asyncio
import json
import random
import re
import sys, os
import threading
import platform

import redis
import requests

from common.ExchangeRate import ExchangeRate
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)
# sys.path.append('/code')
sys.path.append('/')
sys.path.append('/app')
sys.path.append('/root/code')


##自动审查状态为0的订单
from getProductInfo import getOzonProductInfo,getWbProductInfo
from db.client_mongdb import Mongo_ozon, login_mongdb_review
import datetime
r_db = redis.Redis(host='************', port=6379, db=1, password="AirSmile@1688", )
db_client = Mongo_ozon()
db_client.db = db_client.client['review']
db = db_client.db
ex_rate = ExchangeRate(r_db)


def getCostAndPrice(data):
    rate = ex_rate.get_rate()
    date_str = datetime.datetime.now().strftime("%Y-%m-%d")
    current_user = db['userinfo'].find_one({'userId':data['userId']})
    if data['orderPcs'] > 0:
        esCost = round(data['price'] * rate / 100 * data['orderPcs'], 2)
        orderPcs = current_user['info'].get('PURCHASE_ORDER_BASE_PRICE')

        like = 0 if data['like'] == 0 else current_user['info'].get('PURCHASE_ORDER_LIKE_PRICE')

        cart = 0 if data['cart'] == 0 else current_user['info'].get('PURCHASE_ORDER_ADD_ON_PRICE')
        review = 0 if data['review'] == 0 else current_user['info'].get('PURCHASE_ORDER_REVIEW_PRICE')
        espay = (like + cart + review + orderPcs) * current_user['info'].get('FEE_DISCOUNT')
    else:
        esCost = 0
        orderPcs = 0
        like = 0 if data['like'] == 0 else current_user['info'].get('UNPURCHASED_ORDER_ONLY_LIKE_PRICE')
        cart = 0 if data['cart'] == 0 else current_user['info'].get('UNPURCHASED_ORDER_ONLY_ADD_ON_PRICE')
        review = 0
        espay = (like + cart + review + orderPcs) * current_user['info'].get('FEE_DISCOUNT')

    data['esPay'] = round(espay, 2)
    data['esCost'] = round(esCost, 2)
    return data


def review_order_status_0():

    # 审核状态为0的订单
    orders = db.orders.find({"status": 0},{ '_id':0,})
    for order in orders:
        if order['platform'] == 'ozon':
            data = getOzonProductInfo(order['productId'])#获取ozon的商品信息
            if data:
                order.update(data)
                order = getCostAndPrice(order)
                order.update({'status': 1,'updateTime': datetime.datetime.now(),})
                # 更新订单状态 ,通过productId更新对应信息
                result =db['orders'].update_one({"taskId": order['taskId']}, {"$set": order})
                #判断是否更新成功
                if result.modified_count == 1:
                    print(order['productId'],order['taskId'],"更新订单状态成功")
                else:
                    print(order['productId'],order['taskId'],"更新订单状态失败")

        elif order['platform'] == 'wb':
            data = getWbProductInfo(order['productId'])#获取wb的商品信息
            if data:
                order.update(data)
                order = getCostAndPrice(order)
                order.update({'status': 1,'updateTime': datetime.datetime.now(),})
                # 更新订单状态 ,通过productId更新对应信息
                result =db['orders'].update_one({"taskId": order['taskId']}, {"$set": order})
                #判断是否更新成功
                if result.modified_count >= 1:
                    print(order['productId'],order['taskId'],"更新订单状态成功")
                else:
                    print(order['productId'],order['taskId'],"更新订单状态失败")
        else:
            print(order)

def review_order_not_img():
    # 自动审查状态为1 ,img不存在的订单
    orders = db.orders.find({"status": 1,"img": ''},{ '_id':0,})
    for order in orders:
        if order['platform'] == 'ozon':
            data = getOzonProductInfo(order['productId'])
            if data:
                order.update(data)
                order = getCostAndPrice(order)
                # order.update({'status': 1,'updateTime': datetime.datetime.now(),})
                # 更新订单状态 ,通过productId更新对应信息
                result =db['orders'].update_one({"taskId": order['taskId']}, {"$set": order})
                #判断是否更新成功
                if result.modified_count == 1:
                    print(order['productId'],order['taskId'],"更新订单状态成功")
                else:
                    print(order['productId'],order['taskId'],"更新订单状态失败")

        elif order['platform'] == 'wb':
            data = getWbProductInfo(order['productId'])
            if data:
                order.update(data)
                order = getCostAndPrice(order)
                # order.update({'status': 1,'updateTime': datetime.datetime.now(),})
                # 更新订单状态 ,通过productId更新对应信息
                result =db['orders'].update_one({"taskId": order['taskId']}, {"$set": order})
                #判断是否更新成功
                if result.modified_count >= 1:
                    print(order['productId'],order['taskId'],"更新订单状态成功")
                else:
                    print(order['productId'],order['taskId'],"更新订单状态失败")
        else:
            print(order)
if __name__ == '__main__':
    review_order_status_0()