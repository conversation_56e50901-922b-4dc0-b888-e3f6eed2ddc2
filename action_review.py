import base64
import os
import re
import time
import mimetypes
from typing import Optional
from urllib.parse import urlparse
from io import BytesIO

from PIL import Image
import redis
import utils
from barcode import Code128
from retrying import retry
from pyzbar.pyzbar import decode
import requests
from playwright.sync_api import Page
from barcode.writer import ImageWriter
from change_ip import ChangeIP
from config import get_default_config_path, load_config
from db.client_mongdb import login_mongdb_review
from datetime import datetime, time, timedelta
import random

from dingding_robt import DingTalkRobot, MessageBuilder
from hubstudio_sdk import HubStudioClient, HubStudioError
from info import BuyerInfo
import json

from logs import get_logger
from server import ProxyServer

# 创建logging实例
logging = get_logger("")
db = login_mongdb_review()
r_db = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
ip = ChangeIP(r_db, None)
robot = DingTalkRobot()
proxies = {
    'http': '****************************************',
    'https': '****************************************',
}


def is_ozon_login(page: Page, buyer):
    page.goto('https://www.ozon.ru', wait_until="networkidle", timeout=10000000)
    page.wait_for_timeout(1_000)
    if page.get_by_text('Войти').is_visible():
        logging.info(f'账号:{buyer} ozon未登录')
        robot.send(
            message_type="text",
            content=MessageBuilder.text(f"ozon账号:{buyer} 错误 cookies 无法加载"),
            at_all=False
        )
        page.pause()


def is_wb_login(page: Page, buyer):
    page.goto('https://www.wildberries.ru', wait_until="networkidle", timeout=10000000)
    page.wait_for_timeout(1_000)
    if page.get_by_text('Войти').count() > 0:
        logging.info(f'账号:{buyer} wb未登录')
        robot.send(
            message_type="text",
            content=MessageBuilder.text(f"账号:{buyer} WB错误 cookies 无法加载"),
            at_all=False
        )
        page.pause()


class CustomWriter(ImageWriter):
    def __init__(self, extra_text, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.extra_text = extra_text


@retry(stop_max_attempt_number=3, wait_fixed=10000)
def get_wb_code(page: Page, buyer):
    # 查询账号今天是否获取了取件码
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    if db['pickup_info'].find_one({'platform': 'wb', 'buyer': buyer, 'date_at': {'$gte': today}}) is not None:
        logging.info(f'账号:{buyer},wb今天已经获取过取件码')
        return False
    pickup_info = {'platform': None, 'address': None, 'pids': [], 'code': None, 'qrcodeImg': None, 'date_at': None,
                   'return_date': None}
    is_up = False
    with page.expect_response(re.compile(r'wbxoofex\.wildberries\.ru/api/v2/orders')) as response_info:
        page.goto('https://www.wildberries.ru/lk/myorders/delivery')
        page.wait_for_timeout(5000)
        page.wait_for_load_state('networkidle')
        item_num = page.locator('div.delivery-block__list > div').count()
        if item_num == 0:
            logging.info(f'账号:{buyer},wb无货取')
            return False
        is_up = True
        logging.info(f'账号:{buyer},可能有货取,件数:{item_num}件')
    data_dicts = response_info.value.json().get('data', [])
    for data_dict in data_dicts:
        for rid in data_dict.get('rids'):
            expiry_dt = rid.get('expiry_dt', 0)  # 可以取货才有过期时间
            if expiry_dt > 0:
                pickup_info['pids'].append(rid.get('nm_id'))
                pickup_info['return_date'] = datetime.fromtimestamp(expiry_dt)

    if is_up and len(data_dicts) == 0:
        with page.expect_response(
                re.compile(r'www\.wildberries\.ru/webapi/v2/lk/myorders/delivery/active')) as response_info:
            page.goto('https://www.wildberries.ru/lk/myorders/delivery')
            page.wait_for_timeout(5000)
            page.wait_for_load_state('networkidle')

        data_dicts = response_info.value.json().get('value', {}).get('positions', [])
        for data_dict in data_dicts:
            if data_dict.get('trackingStatusReady', False):  # 取货表示可以取货
                pickup_info['pids'].append(data_dict.get("code1S", 0))
                temp_expiry_t = int(data_dict.get('expirationTimestamp', 0) / 1000)
                temp_expiry = datetime.fromtimestamp(temp_expiry_t)
                if pickup_info['return_date'] is None or pickup_info['return_date'] > temp_expiry:
                    pickup_info['return_date'] = temp_expiry

    if len(pickup_info['pids']) == 0:
        logging.info('账号:{},wb无货取'.format(buyer))
        return False
    qrcode_base64 = page.locator('.delivery-qr__code').get_attribute('src')
    pickup_info['address'] = page.locator('.delivery-address__info').first.text_content().strip()
    pickup_info['qrcodeImg'] = str(qrcode_base64).split(',', -1)[1]
    pickup_info['date_at'] = datetime.now()
    binary_data = base64.b64decode(pickup_info['qrcodeImg'])
    pickup_info['code'] = decode(Image.open(BytesIO(binary_data)))[0].data.decode('ascii')
    pickup_info['buyer'] = buyer
    pickup_info['platform'] = 'wb'

    db['pickup_info'].replace_one(
        {'platform': 'wb', 'buyer': buyer, 'code': pickup_info['code'], 'address': pickup_info['address']}, pickup_info,
        True)
    return True


@retry(stop_max_attempt_number=3, wait_fixed=10000)
def get_wb_code_return(page: Page, buyer):
    # 查询账号今天是否获取了取件码
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    if db['pickup_info'].find_one({'platform': 'wb', 'buyer': buyer, 'date_at': {'$gte': today}}) is not None:
        logging.info(f'账号:{buyer},wb今天已经获取过取件码')
        return None
    pickup_info = {'platform': None, 'address': None, 'pids': [], 'code': None, 'qrcodeImg': None, 'date_at': None,
                   'return_date': None}
    data_dicts = []
    with page.expect_response(re.compile(r'wildberries\.ru/webapi/v2/lk/myorders/delivery/active')) as response_info:
        page.goto('https://www.wildberries.ru/lk/myorders/delivery')
        page.wait_for_timeout(5000)
        page.wait_for_load_state('networkidle')
    data_dicts = response_info.value.json().get('value', {}).get('positions', [])
    for data_dict in data_dicts:
        if data_dict.get('trackingStatusReady', False):
            pickup_info['pids'].append(data_dict.get("code1S", 0))
            temp_expiry_t = int(data_dict.get('expirationTimestamp', 0) / 1000)
            temp_expiry = datetime.fromtimestamp(temp_expiry_t)
            if pickup_info['return_date'] is None or pickup_info['return_date'] > temp_expiry:
                pickup_info['return_date'] = temp_expiry

    if len(pickup_info['pids']) == 0:
        logging.info('账号:{},wb无货取'.format(buyer))
        return
    pickup_info['address'] = page.locator('.delivery-address__info').first.text_content().strip()
    qrcode_base64 = page.locator('.delivery-qr__code').get_attribute('src')
    pickup_info['qrcodeImg'] = str(qrcode_base64).split(',', -1)[1]
    pickup_info['date_at'] = datetime.now()
    binary_data = base64.b64decode(pickup_info['qrcodeImg'])
    pickup_info['code'] = decode(Image.open(BytesIO(binary_data)))[0].data.decode('ascii')
    pickup_info['buyer'] = buyer
    pickup_info['platform'] = 'wb'

    db['pickup_info'].replace_one(
        {'platform': 'wb', 'buyer': buyer, 'code': pickup_info['code'], 'address': pickup_info['address']}, pickup_info,
        True)


@retry(stop_max_attempt_number=3, wait_fixed=10000)
def get_ozon_code(page: Page, buyer):
    """
    获取Ozon平台的取货码信息

    Args:
        page: Playwright页面对象
        buyer: 买家账号

    Returns:
        None: 如果没有可取货的订单
    """
    # 查询账号今天是否获取了取件码
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    if db['pickup_info'].find_one({'platform': 'ozon', 'buyer': buyer, 'date_at': {'$gte': today}}) is not None:
        logging.info(f'账号:{buyer},ozon今天已经获取过取件码')
        return False
    # 导航到订单列表页面
    page.goto('https://www.ozon.ru/my/orderlist?sort=2', wait_until="networkidle", timeout=10000000)
    page.wait_for_timeout(5000)
    # page.pause()

    # 获取页面HTML内容
    body_str = page.inner_html('body')

    # 检查是否有订单
    is_not_order = re.findall('state-orderList-(.+)"', body_str)
    if len(is_not_order) == 0:
        logging.info(f'账号:{buyer},ozon无货取')
        return False
    #
    # 提取取货地址
    address_matches = re.findall(',"fullName":"(.*?)","name":"', body_str)
    if not address_matches:
        logging.warning(f'账号:{buyer},无法提取取货地址')
        return False
    address_txt = address_matches[0]

    # 检查是否有取货码
    receipt_code_elements = page.locator('div[id^="state-receiptCode"]')
    if receipt_code_elements.count() <= 0:
        logging.info(f'账号:{buyer},ozon无货取码')
        return False

    # 提取取货码
    receipt_code_data = receipt_code_elements.get_attribute('data-state')
    if not receipt_code_data:
        logging.warning(f'账号:{buyer},取货码数据为空')
        return False

    receipt_code_json = json.loads(receipt_code_data)
    if not receipt_code_json or 'title' not in receipt_code_json or 'text' not in receipt_code_json.get('title', {}):
        logging.warning(f'账号:{buyer},取货码数据格式不正确')
        return False

    temp_str = receipt_code_json.get('title').get('text')
    bar_code_txt = str(temp_str).replace(' — код для получения в пунктах выдачи и постаматах Ozon', '').replace(' ', '',
                                                                                                                -1)

    # 生成条形码图片
    img = Code128(bar_code_txt, writer=CustomWriter(extra_text=buyer[:-5]))
    img.save("1233")

    # 读取并编码图片
    if not os.path.exists("1233.png"):
        logging.warning(f'账号:{buyer},条形码图片生成失败')
        return False

    with open("1233.png", "rb") as f:
        qrcode_img = base64.b64encode(f.read()).decode('utf-8')

    # 删除临时文件
    os.remove("1233.png")

    # 初始化变量
    pids = []
    order_list = []
    return_date = None

    # 获取所有订单数据
    order_locators = page.locator('div[id^="state-orderList"]').all()
    for po in order_locators:
        order_data_attr = po.get_attribute('data-state')
        if not order_data_attr:
            continue

        order_data = json.loads(order_data_attr)
        order_list_data = order_data.get('orderList')

        if not order_list_data:
            continue

        if len(order_list) == 0:
            order_list = order_list_data
            continue

        for order in order_list_data:
            order_list.append(order)

    # 处理订单数据
    for order in order_list:
        header = order.get('header', {})
        sections = order.get('sections', [])

        for section in sections:
            status = section.get('status', {})
            status_name = status.get('name', '')

            # 跳过准备发货的订单
            if status_name == 'В сборке':  # 订单准备发货
                continue

            # 处理取消的订单
            if status_name == 'Отменён':  # 取消的订单
                logging.info(f"{buyer}:的OZON订单，有单被取消")
                db['orders'].update_many(
                    {'platform': 'ozon', 'buyer': buyer, 'status': 3, 'orderId': header.get('number')},
                    {'$set': {'status': 2, 'updateTime': datetime.now()}})
                continue

            # 处理已收到的订单
            if status_name == 'Получен':  # 已收到
                order_update_result = db['orders'].update_many(
                    {'platform': 'ozon', 'buyer': buyer, 'status': 3, 'orderId': header.get('number')},
                    {'$set': {'status': 4, 'updateTime': datetime.now()}})

                if order_update_result.modified_count == 0:
                    logging.info(f"{buyer}:的订单:{header.get('number')}状态未更新")
                else:
                    logging.info(f"更新订单:{header.get('number')}状态 已收货")
                continue

            # 判断是否到达派送点
            description = section.get('description')
            if description is not None:
                continue

            # 提取最后取货日期
            if status_name.startswith('Ожидает получения до '):
                return_date = utils.ozon_ru_to_time(status_name.replace('Ожидает получения до ', ''))

            # 提取产品ID
            products = section.get('products', [])
            if not products:
                continue

            for product in products:
                product_link = product.get('link', '')
                if not product_link:
                    continue

                # 提取产品ID
                if '/product/' in product_link:
                    pid_matches = re.findall(r'-(\d+)?/', product_link)
                    if pid_matches:
                        pids.append(pid_matches[0])
                else:
                    order_matches = re.findall(r'order=(.+)?', product_link)
                    if order_matches:
                        pids.append(order_matches[0])

    # 如果没有可取货的产品，返回None
    if not pids:
        logging.info(f'账号:{buyer},ozon无可取货产品')
        return False

    # 构建取货信息
    pickup_info = {
        "platform": "ozon",
        "address": address_txt,
        "pids": pids,
        "code": bar_code_txt,
        "qrcodeImg": qrcode_img,
        "date_at": datetime.now(),
        "return_date": return_date,
        "buyer": buyer,
    }

    # 保存到数据库
    db['pickup_info'].replace_one(
        {'platform': 'ozon', 'buyer': buyer, 'code': bar_code_txt},
        pickup_info, True)

    logging.info(f'账号:{buyer},成功获取ozon取货码,共{len(pids)}个产品')


@retry(stop_max_attempt_number=3, wait_fixed=10000)
def action_ozon_check_order_status(page, buyer):
    page.goto('https://www.ozon.ru/my/reviews')
    page.wait_for_load_state('networkidle')
    logging.info('进入ozon订单列表页')
    page.wait_for_timeout(5_000)
    data_locator = page.locator('div[id^="state-rateBoughtItems"]').all()
    if len(data_locator) == 0:
        ors_result = db['orders'].find({'buyer': buyer, 'platform': 'ozon', 'status': {'$in': [3, 4]}})
        for order in ors_result:
            if order.get('status') == 3:
                logging.info('{} 订单:{} 产品:{}  未收货'.format(buyer, order['taskId'], order['productId']))
        logging.info('没订单可评论')
        return None
    if len(data_locator) == 1:
        data_str = data_locator[0].get_attribute('data-state')
        data_items = json.loads(data_str).get('items')
        new_data_items = []
        for item in data_items:
            pid = int(item.get('id'))
            order = db['orders'].find_one(
                {'buyer': buyer, 'status': {'$in': [3, 4]}, 'platform': 'ozon', 'productId': pid})
            if order is None:
                logging.info('{} 产品:{}  找不到订单'.format(buyer, pid))
                re_order = db['orders'].find_one(
                    {'productId': pid, 'platform': 'ozon', "status": {'$nin': [1, 3, 4, 5]}})
                if re_order is None:
                    logging.info('{} 产品:{}  再次找不到订单'.format(buyer, pid))
                    continue
                else:
                    update_result = db['orders'].update_one({'taskId': re_order['taskId']}, {
                        '$set': {'buyer': buyer, 'status': 4, 'updateTime': datetime.now()}})
                    if update_result.modified_count == 1:
                        logging.info('{} 订单:{} 产品:{} 绑定买家'.format(buyer, re_order['taskId'], pid))
                        re_order['buyer'] = buyer
                        re_order['status'] = 4
                        order = re_order
            item['order'] = order
            parsed_url = urlparse(item.get('image'))
            file_name = os.path.basename(parsed_url.path)
            item['image'] = file_name
            match order.get('status', None):
                case 3:
                    db['orders'].update_one({'buyer': buyer, 'productId': pid},
                                            {'$set': {'status': 4, 'updateTime': datetime.now()}})  # 更新订单状态
                    logging.info('{} 订单:{} 产品:{} 标记已取货'.format(buyer, order['taskId'], pid))
                    new_data_items.append(item)
                case 4:
                    logging.info('{} 订单:{} 产品:{}  已收货'.format(buyer, order['taskId'], pid))
                    new_data_items.append(item)
                case 5:
                    logging.info('{} 订单:{} 产品:{}  已评论'.format(buyer, order['taskId'], pid))
        return new_data_items


@retry(stop_max_attempt_number=3, wait_fixed=10000)
def action_wb_review(page, buyer, review_items):
    if review_items is None:
        return False
    if len(review_items) == 0:
        logging.info('没有待评论订单')
        return False
    logging.info('共有{}个待评论订单'.format(len(review_items)))
    e_nth = 0
    for item in review_items:
        order = item.get('order', None)
        if order is None:
            e_nth += 1
            continue
        with page.expect_response(
                lambda response: f'goodsArchive?nmId={item['code1S']}' in response.url) as response_info:
            logging.info('点击-评论产品:{} 按钮'.format(item['code1S']))
            product_loc = page.locator(f'img[src*="{item['code1S']}"]').locator('..')  # 获取父元素
            product_loc.locator('ul.delivery-rate__rate-star li[data-rating="5"]').click()  # 点击五星
            # page.locator("#app").get_by_role("list").nth(e_nth).click()
            page.wait_for_timeout(1_000)
        if response_info.value.status == 200:
            logging.info('打开-评论产品:{} 评论页面'.format(item['code1S']))
            page.wait_for_timeout(1_000)
            page.locator('.popup-review__rate-wrap ul.rate-star>li').first.click()  # 点击五星
            review_text = order.get('reviewTextInput', '')
            if review_text != '':
                page.locator('#commentTextarea').type(review_text, timeout=300000, delay=113)  # 填写评论
                # page.wait_for_timeout(30_000)
            # 判断是否上传视频
            video_name = order.get('reviewVideoInput', None)
            if video_name is not None and video_name != "":
                mime_type, _ = mimetypes.guess_type(video_name)
                video_resp = requests.get(f'https://www.svetx.com/video/{video_name}')
                if video_resp.status_code == 200:
                    logging.info('上传评论视频:{}'.format(video_name))
                    data_files = [{"name": video_name, "mimeType": mime_type, "buffer": video_resp.content}]
                    page.locator('#video-load').set_input_files(files=data_files, timeout=600_000)
                    logging.info('完成评论视频上传:{}'.format(video_name))
                    if page.locator('text="Видео длится больше 2 минут, загрузите другое"').count() > 0:
                        logging.info('视频长度过长，请修改小于2分钟')
                        db['orders'].update_one({'taskId': order['taskId']},
                                                {'$set': {'remark': '视频长度过长，请修改小于2分钟'}})
                        continue
                    page.pause()
                else:
                    logging.info('评论视频 404错误:{},上传失败'.format(video_name))
                    db['orders'].update_one({'taskId': order['taskId']},
                                            {'$set': {'remark': video_name + ' 视频无法链接'}})
                    continue
            # 判断是否上传图片
            e_input_img = page.locator('input[id="img-load"]')
            review_img_list = order.get('reviewImgInput', [])
            if len(review_img_list) > 0:
                logging.info('评论图片张数:{}'.format(len(review_img_list)))
            for img_name in review_img_list:
                mime_type, _ = mimetypes.guess_type(img_name)
                img_resp = requests.get(f'https://www.svetx.com/img/{img_name}')
                if img_resp.status_code == 200:
                    logging.info('上传评论图片:{}'.format(img_name))
                    data_files = [{"name": img_name, "mimeType": mime_type, "buffer": img_resp.content}]
                    e_input_img.set_input_files(files=data_files, timeout=120_000)
                    logging.info('完成评论图片上传:{}'.format(img_name))
                    if page.locator(
                            'text="Фото не подходит: нужно не меньше 337 px по ширине и 450 px по высоте"').count() > 0:
                        logging.info('图片尺寸过小')
                        db['orders'].update_one({'taskId': order['taskId']}, {'$set': {'remark': '图片尺寸过小'}})
                        continue
                    if page.locator('text="Не получилось загрузить, попробуйте еще раз"').count() > 0:
                        logging.info('上传失败,跳过')
                        db['orders'].update_one({'taskId': order['taskId']}, {'$set': {'remark': '上传失败'}})
                        continue
                    page.wait_for_timeout(5_000)
                else:
                    logging.info('评论图片 404错误:{},上传失败'.format(img_name))
                    db['orders'].update_one({'taskId': order['taskId']},
                                            {'$set': {'remark': img_name + ' 图片无法链接'}})
                    page.pause()
                    continue
            page.wait_for_timeout(1_000)
            page.click('button:has-text("Отправить отзыв")')
            page.wait_for_timeout(1_000)
            if page.get_by_role("button", name="Отправить как есть").is_visible():
                page.click('button:has-text("Отправить как есть")')
                page.wait_for_timeout(1_000)
            page.get_by_role("button", name="Хорошо").click()
            page.wait_for_timeout(1_000)
            db['orders'].update_one({'taskId': order['taskId']},
                                    {'$set': {'status': 5,
                                              'updateTime': datetime.now()}})  # 更新订单状态
        else:
            logging.info('评论产品{} 页面打开失败'.format(item['code1S']))
        page.wait_for_timeout(1_000)


@retry(stop_max_attempt_number=3, wait_fixed=10000)
def action_ozon_review1(page, buyer, review_items):
    if review_items is None:
        return False
    if len(review_items) == 0:
        logging.info('没有待评论订单')
        return False
    logging.info('共有{}个待评论订单'.format(len(review_items)))
    for i in range(len(review_items)):
        with page.expect_response(re.compile(r'%2Fmodal%2Fcreate-review%3Fitem_ids')) as response_info:
            page.locator('div[data-widget="rateBoughtItems"]>div>div>div>div').first.click()
        state_dict = response_info.value.json().get('widgetStates')
        state_json = None
        item = None
        order = None
        for key, value in state_dict.items():
            if 'rateBoughtForm' in key:
                state_json = json.loads(value)
        if state_json is not None:
            product_id = int(state_json.get('productId'))
            for r_item in review_items:
                tempe = r_item.get('order', None)
                if tempe.get('productId') == product_id:
                    r_item['questions'] = state_json.get('contextQuestions', [])
                    item = r_item
                    order = tempe

        if item is not None and order is not None and page.wait_for_selector(
                'div[data-widget="modalLayout"]').is_visible():
            logging.info('点击5星评价')
            page.locator(
                'div[data-widget="modalLayout"] > div > div > div > div > div > div:nth-child(1) > svg').last.click()
            page.wait_for_timeout(710)
            logging.info('填写文字评论')
            review_text = order.get('reviewTextInput', '')
            page.locator('div[data-widget="modalLayout"]>div:has-text("Общее впечатление") textarea').fill(review_text)
            e_input_media = page.locator('#mediaPreloadReviewsDropzone')
            review_img_list = order.get('reviewImgInput', [])
            if len(review_img_list) > 0:
                logging.info('评论图片张数:{}'.format(len(review_img_list)))
            for img_name in review_img_list:
                mime_type, _ = mimetypes.guess_type(img_name)
                img_resp = requests.get(f'https://www.svetx.com/img/{img_name}')
                if img_resp.status_code == 200:
                    logging.info('上传评论图片:{}'.format(img_name))
                    e_input_media.set_input_files(files=[
                        {"name": img_name, "mimeType": mime_type, "buffer": img_resp.content}
                    ])
                    while 1:  # disabled
                        time.sleep(0.1)
                        if page.locator('button:has-text("Отправить отзыв")[disabled="disabled"]').count() == 0:
                            logging.info('评论图片:{},上传成功'.format(img_name))
                            break
                    page.wait_for_timeout(1_000)
                else:
                    logging.info('评论图片 404错误:{},上传失败'.format(img_name))

            video_name = order.get('reviewVideoInput', None)
            if video_name is not None and video_name == 1:
                mime_type, _ = mimetypes.guess_type(video_name)
                video_resp = requests.get('https://www.svetx.com/video/{}'.format(video_name))
                if video_resp.status_code == 200:
                    video_files = [{"name": video_name, "mimeType": mime_type, "buffer": video_resp.content}]
                    e_input_media.set_input_files(files=video_files)
                    while 1:  # disabled
                        time.sleep(0.1)
                        if page.locator('button:has-text("Отправить отзыв")[disabled="disabled"]').count() == 0:
                            logging.info('评论视频:{},上传成功'.format(video_name))
                            break
                    page.wait_for_timeout(1000)
                else:
                    logging.info('评论视频 404错误:{},上传失败'.format(video_name))

            for question in item.get('questions', []):
                logging.info('点击{}问题 选择:{}'.format(question.get('title').get('text', ""),
                                                         question['variants'][-1].get('title')))
                page.locator(
                    f'#ctxAnswers{question.get("id")} >div>div:has-text("{question['variants'][-1].get('title')}")').last.click()
                page.wait_for_timeout(5_00)

            page.get_by_role("button", name="Отправить отзыв").click()
            page.wait_for_load_state('networkidle')

            logging.info('更新订单已评论状态:productId:{},buyer:{},status:5'.format(order['productId'], buyer))
            db['orders'].update_one({'taskId': order['taskId']},
                                    {'$set': {'status': 5, 'updateTime': datetime.now()}})  # 更新订单状态
            logging.info('完成订单')
            page.wait_for_timeout(1_000)
            page.mouse.click(x=120, y=0)
            page.wait_for_timeout(2_000)


@retry(stop_max_attempt_number=3, wait_fixed=10000)
def action_ozon_review2(page, buyer, review_items):
    if review_items is None:
        return False
    if len(review_items) == 0:
        logging.info('没有待评论订单')
        return False
    logging.info('共有{}个待评论订单'.format(len(review_items)))
    with page.expect_response(re.compile(r'%2Fmodal%2Fcreate-review%3Fitem_ids')) as response_info:
        page.locator(f'//div[contains(@style, "{review_items[0]['image']}")]').first.click()
    state_dict = response_info.value.json().get('widgetStates')
    state_json = None
    match_flag = False
    for key, value in state_dict.items():
        state_json = json.loads(value)
    if state_json is not None:
        state_items = state_json.get('items', [])
        new_review_items = []
        if len(review_items) != 1 and len(state_items) != len(review_items):
            logging.info('评论数据和需评论数量不一致，将开始点击产品做精准匹配')
            match_flag = True
        for state_item in state_items:
            for i, _ in enumerate(review_items):
                if state_item.get('id') == review_items[i]['id']:
                    state_item['order'] = review_items[i]['order']
                    new_review_items.append(state_item)
        if len(new_review_items) != 0:
            review_items = new_review_items

    page.wait_for_load_state('networkidle')
    if page.wait_for_selector('div[data-widget="modalLayout"]').is_visible():
        for item in review_items:
            order = item.get('order', None)
            if match_flag:
                logging.info(f'开始点击产品{item['id']},做精准匹配')
                l_img = page.locator(f'div[data-widget="modalLayout"] > img[src*="{item['image'].split('/').pop()}"]')
                if l_img.count() == 1:
                    l_img.click()
                elif l_img.count() > 1:
                    l_img.first.click()
                page.wait_for_timeout(2_000)
            logging.info('点击5星评价')
            page.locator('div[data-widget="modalLayout"] label>div svg').last.click()  # 5星评价
            logging.info('填写文字评论')
            review_text = order.get('reviewTextInput', '')
            page.locator('p:has-text("Достоинства")').locator('..').locator('textarea').fill(review_text)

            e_input_img = page.locator('form[novalidate="novalidate"] input[accept=".jpg, .jpeg, .png"]')
            review_img_list = order.get('reviewImgInput', [])
            if len(review_img_list) > 0:
                logging.info('评论图片张数:{}'.format(len(review_img_list)))
            for img_name in review_img_list:
                mime_type, _ = mimetypes.guess_type(img_name)
                img_resp = requests.get(f'https://www.svetx.com/img/{img_name}')
                if img_resp.status_code == 200:
                    logging.info('上传评论图片:{}'.format(img_name))
                    e_input_img.set_input_files(files=[
                        {"name": img_name, "mimeType": mime_type, "buffer": img_resp.content}
                    ])
                    while 1:  # disabled
                        time.sleep(0.1)
                        if page.locator('button:has-text("Отправить отзыв")[disabled="disabled"]').count() == 0:
                            logging.info('评论图片:{},上传成功'.format(img_name))
                            break
                    page.wait_for_timeout(1_000)
                else:
                    logging.info('评论图片 404错误:{},上传失败'.format(img_name))

            video_name = order.get('reviewVideoInput', None)
            if video_name is not None and video_name == 1:
                mime_type, _ = mimetypes.guess_type(video_name)
                video_resp = requests.get('https://www.svetx.com/video/{}'.format(video_name))
                if video_resp.status_code == 200:
                    video_files = [{"name": video_name, "mimeType": mime_type, "buffer": video_resp.content}]
                    page.locator('input[accept="video/*"]').set_input_files(video_files)
                    while 1:  # disabled
                        time.sleep(0.1)
                        if page.locator('button:has-text("Отправить отзыв")[disabled="disabled"]').count() == 0:
                            logging.info('评论视频:{},上传成功'.format(video_name))
                            break
                    page.wait_for_timeout(1000)

            for question in item.get('contextQuestions', []):
                page.locator(f'input[name="contextualQuestion{question.get("id")}"]').last.click()
                page.wait_for_timeout(5_00)

            page.get_by_role("button", name="Отправить отзыв").click()
            page.wait_for_load_state('networkidle')
            logging.info('更新订单已评论状态:productId:{},buyer:{},status:5'.format(order['productId'], buyer))
            db['orders'].update_one({'taskId': order['taskId']},
                                    {'$set': {'status': 5, 'updateTime': datetime.now()}})  # 更新订单状态
            logging.info('完成订单')
            page.wait_for_timeout(3_000)


@retry(stop_max_attempt_number=3, wait_fixed=10000)
def action_wb_check_order_status(page, buyer):
    with page.expect_response(re.compile(r'myorders/goods/notevaluated')) as response_info:
        page.goto('https://www.wildberries.ru/lk/myorders/delivery')  # 查看取货订单
    logging.info('进入wildberries订单列表页')
    wb_wait_review_items = response_info.value.json().get('value', [])
    if len(wb_wait_review_items) == 0:
        ors_result = db['orders'].find({'buyer': buyer, 'platform': 'wb', 'status': {'$in': [3, 4]}})
        logging.info('没订单可评论')
        for order in ors_result:
            if order.get('status') == 3:
                logging.info('{} 订单:{} 产品:{}  未收货'.format(buyer, order['taskId'], order['productId']))
        return None
    for i, wait_review_item in enumerate(wb_wait_review_items):
        pid = int(wait_review_item['code1S'])
        order = db['orders'].find_one({'buyer': buyer, 'productId': pid}, {'_id': 0})  # 查询订单
        if order is None:
            logging.info('{} 产品:{} 找不到订单'.format(buyer, pid))
            continue
        wb_wait_review_items[i]['order'] = order
        # 更新订单状态
        match order.get('status', None):
            case 3:
                db['orders'].update_one({'buyer': buyer, 'productId': pid}, {
                    '$set': {'status': 4, 'updateTime': datetime.now()}})  # 更新订单状态
                logging.info('{} 订单:{} 产品:{} 标记已取货'.format(buyer, order['taskId'], pid))
            case 4:
                logging.info('{} 订单:{} 产品:{}  已收货'.format(buyer, order['taskId'], pid))
            case 5:
                logging.info('{} 订单:{} 产品:{}  已评论'.format(buyer, order['taskId'], pid))
            case -1:
                logging.info('{} 订单:{} 产品:{}  已完成'.format(buyer, order['taskId'], pid))
    return wb_wait_review_items


def search_by_product_id(page, product_id, item, buyer):
    """通过商品ID搜索并添加商品"""
    with page.expect_response(lambda response: re.compile(r'card\.wb\.ru/cards').search(response.url)) as resp_info:
        page.get_by_role("searchbox", name="Найти на Wildberries").type(product_id, delay=217)
        page.get_by_label("Поиск товара").click()
    
    page.wait_for_timeout(8000)
    page.wait_for_load_state("networkidle")
    
    search_temp = resp_info.value.json().get('data')
    if not search_temp:
        logging.info(f'商品:{product_id} 搜索失败')
        return False
        
    search_items = search_temp.get('products', [])
    if len(search_items) != 1:
        logging.info(f'商品:{product_id} 没有找到')
        return False
        
    if search_items[0].get('totalQuantity', 0) <= 0:
        logging.info(f'商品:{product_id} 库存不足')
        return False
        
    cart_price = search_items[0]['sizes'][0]['price']['product'] * 0.01
    if cart_price > item['price']:
        item['price'] = cart_price
        
    page.get_by_role("button", name="Добавить в корзину").click()
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(2000)
    
    logging.info(f'{buyer} 商品:{product_id} 加入购物车 单价:{cart_price}₽')
    return True

def handle_search_results(page, search_temp, item, buyer):
    """处理搜索结果"""
    total = search_temp.get('total', 0)
    if total == 0:
        return False
        
    if total == 1:
        product = search_temp.get('products', [])[0]
        cart_price = product['sizes'][0]['price']['product'] * 0.01
        if cart_price > item['price']:
            item['price'] = cart_price
            
        page.locator(f"#c{item['productId']} .product-card__order-wrap a").click()
        page.wait_for_load_state("networkidle")
        page.wait_for_timeout(2000)
        
        logging.info(f'{buyer} 商品:{item["productId"]} 加入购物车 单价:{cart_price}₽')
        return True
        
    # 处理多个搜索结果
    search_items = search_temp.get('products', [])
    for index, search_item in enumerate(search_items):
        if index in [0, 9, 18, 27, 36, 45, 54, 63, 72, 81]:
            page.wait_for_load_state("networkidle")
            page.wait_for_timeout(920)
            scroll_height = page.evaluate("document.documentElement.scrollHeight")
            viewport_height = page.evaluate("window.innerHeight")
            if scroll_height > viewport_height:
                page.evaluate("window.scrollTo({top: window.scrollY + window.innerHeight, behavior: 'smooth'})")
                page.wait_for_timeout(500)
                
        if search_item['id'] == int(item["productId"]):
            cart_price = search_items[index]['sizes'][0]['price']['product'] * 0.01
            if cart_price > item['price']:
                item['price'] = cart_price
                
            page.locator(f"#c{search_item['id']}").scroll_into_view_if_needed()
            page.locator(f"#c{search_item['id']} .product-card__order-wrap a").first.click()
            page.wait_for_timeout(1020)
            
            logging.info(f'{buyer} 商品:{item["productId"]} 加入购物车 单价:{cart_price}₽')
            return True
            
    return False

def search_and_add_item(page, item, buyer):
    """搜索并添加商品到购物车"""
    product_id = str(item['productId'])
    keyword = item.get('keyword', '')
    
    # 清空搜索框
    if page.get_by_label("Очистить поиск").is_visible():
        page.get_by_label("Очистить поиск").click()
        page.wait_for_timeout(700)
    
    # 如果没有关键词，直接使用商品ID搜索
    if not keyword:
        return search_by_product_id(page, product_id, item, buyer)
        
    # 有关键词时进行关键词搜索
    page.get_by_role("searchbox", name="Найти на Wildberries").type(keyword, delay=217)
    page.wait_for_timeout(1000)
    
    for page_num in range(1, 8):
        with page.expect_response(lambda response: re.compile(r'search\.wb\.ru/exactmatch/ru').search(response.url)) as resp_info:
            if page_num == 1:
                page.get_by_label("Поиск товара").click()
            else:
                next_page = page.get_by_role("link", name=f"{page_num}", exact=True)
                if not next_page.is_visible():
                    break
                next_page.click()
                
            page.wait_for_timeout(8000)
            page.wait_for_load_state("networkidle")
            
            search_temp = resp_info.value.json().get('data')
            if not search_temp:
                continue
                
            if handle_search_results(page, search_temp, item, buyer):
                page.get_by_label("Очистить поиск").click()
                page.wait_for_timeout(800)
                return True
                
    # 关键词搜索失败，尝试商品ID搜索
    return search_by_product_id(page, product_id, item, buyer)

@retry(stop_max_attempt_number=3, wait_fixed=10000)
def buy_wb_item(item_list, buyer, page):
    if len(item_list) == 0:
        return None
        
    new_item_list = []
    r_cart_price = 0
    budget_amount = 0
    
    page.goto('https://www.wildberries.ru')
    page.wait_for_load_state('networkidle')
    
    for item in item_list:
        budget_amount += item['price']
        if search_and_add_item(page, item, buyer):
            new_item_list.append(item)
            r_cart_price += item['price']
            
    if len(new_item_list) == 0:
        return None
        
    page.get_by_role("link", name="Корзина").click()
    
    # 发送纯文本消息
    robot.send(
        message_type="text",
        content=MessageBuilder.text(f'{buyer} 购物车中共有{len(new_item_list)}件商品, 预估金额:{budget_amount}₽'),
        at_all=False
    )
    
    page.pause()
    
    # 等待选择页面支付方式
    with page.expect_request(
            lambda request: re.compile(r'wbxoofex\.wildberries\.ru/api/v9/order\?sticker=').search(request.url),
            timeout=3000_000) as req_info:
        page.get_by_role("button", name="Заказать").click()
        page.wait_for_timeout(random.randint(5, 15) * 1000)
        
    json_order = req_info.value.post_data_json.get('order', {})
    order_id = json_order.get("order_uid", "")
    order_amount = json_order.get("payment", {}).get("amount", 0) * 0.01
    payment_type_name = json_order.get("payment", {}).get("paymentType", {}).get("selectedBankCard", {}).get('name', "")
    bank_card_id = json_order.get("payment", {}).get("paymentType", {}).get("bankCardId", "")
    
    logging.info(f'{buyer} 订单号:{order_id} 支付方式:{payment_type_name} 银行卡:{bank_card_id}')
    logging.info(f'{buyer} 订单号:{order_id} 下单数:{len(new_item_list)}')
    logging.info(f'预估金额:{budget_amount}₽ ,订单金额:{r_cart_price}₽ ,付款金额:{order_amount}₽')
    
    for item in new_item_list:
        price_o = item['price']
        if not ("SberPay" in payment_type_name):
            price_f = int(item['price'] + item['price'] * 0.025)
        else:
            price_f = item['price']
            
        logging.info(f'任务:{item["taskId"]}, 原价格:{price_o}₽, 修正价格:{price_f}₽')
        item['price'] = price_f
        
        updata_data = {
            '$set': {
                'status': 3,
                'orderId': order_id,
                'buyer': buyer,
                'price': item['price'],
                'updateTime': datetime.now()
            }
        }
        db['orders'].update_one({'taskId': item['taskId']}, updata_data)
        
    with page.expect_response(re.compile(r'wbxoofex\.wildberries\.ru/api/v2/orders')) as response_info:
        page.get_by_role("link", name="Перейти в доставки").click()
        page.wait_for_timeout(3_000)
        page.wait_for_load_state('networkidle')
        
    data_dicts = response_info.value.json().get('data', [])
    is_not_pay = False
    for data_dict in data_dicts:
        for rid in data_dict.get('rids'):
            if rid['pay_state'] == 1:
                is_not_pay = True
                break
                
    if is_not_pay:
        logging.info(f'{buyer} 有支付失败商品')
        page.get_by_role("button", name="К оплате").click()
        page.wait_for_timeout(3_000)
        page.wait_for_load_state('networkidle')
        page.locator(f'input[value="{bank_card_id}"]').first.locator('..').click()
        page.wait_for_timeout(800)
        page.locator("button.popup__btn-main").click()
        page.wait_for_timeout(5_000)




def buy_yandex_item(item_list, buyer, page):
    if len(item_list) == 0:
        return None
    page.goto('https://market.yandex.ru/', wait_until="networkidle", timeout=1000000)
    page.locator('#header-search').type(item_list[0]['keyword'], delay=217)
    page.get_by_role("button", name="Найти").click()
    page.pause()
    r_db.set(f"{buyer}_yandex", 1)


@retry(stop_max_attempt_number=3, wait_fixed=10000)
def buy_ozon_item(ozon_items, buyer, page):
    if len(ozon_items) == 0:
        return None
    ozon_page_num = 1
    data_jsons = []
    data_page_list = []
    page.goto('https://www.ozon.ru')
    page.wait_for_load_state('networkidle')
    def handle_response(response):
        if response.status == 200:
            url = response.request.url
            if ('entrypoint-api.bx/page/json/v2?url=%2Fcategory%2F' in url) or (
                    'entrypoint-api.bx/page/json/v2?url=%2Fsearch%2F' in url):
                data_json = response.json()
                if data_json is None:
                    return
                ws_json = data_json.get('widgetStates', [])
                for key in ws_json:
                    sr_dict = ws_json.get(key, None)
                    if sr_dict is None:
                        continue
                    sr_json = json.loads(sr_dict)
                    if sr_json.get('page') is not None and sr_json.get('page') != '':
                        print(sr_json.get('page'))
                        data_jsons.append(sr_json)
                        data_page_list.append(sr_json)

    new_ozon_items = []
    r_cart_price = 0
    budget_amount = 0
    for i in range(len(ozon_items)):
        budget_amount += ozon_items[i]['price']
        keyword_txt = ozon_items[i]['keyword']
        product_id = str(ozon_items[i]['productId'])
        if keyword_txt != '':
            page.get_by_placeholder("Искать на Ozon").type(keyword_txt, delay=267)
            page.wait_for_load_state("networkidle")
            flag = True
            page.on('response', handle_response)
            while flag:
                if len(data_jsons) >= 30:
                    keyword_txt = ''
                    flag = False
                    page.wait_for_timeout(1000)
                    page.get_by_placeholder("Искать на Ozon").locator('..').locator('div > svg').click()
                    page.wait_for_timeout(1000)
                    page.locator("form path").nth(1).click()
                # 尝试查找元素
                for data in data_page_list:
                    items = data.get('items')
                    for item in items:
                        if item.get('skuId') == product_id:
                            new_ozon_items.append(ozon_items[i])
                            current_cart_num = item.get('multiButton').get('ozonButton').get(
                                'addToCartButtonWithQuantity').get('currentItems', 0)
                            if current_cart_num > 0:
                                print('商品已存在购物车')
                            else:
                                link = item.get('action').get('link')
                                btn_text = item.get('multiButton').get('ozonButton').get(
                                    'addToCartButtonWithQuantity').get('text')
                                page.locator(f'a[href="{link}"]').first.locator('..').get_by_role("button",
                                                                                                  name=f'{btn_text}').click()
                                print('找到商品')
                            # 清空
                            flag = False
                            page.wait_for_timeout(1000)
                            page.get_by_placeholder("Искать на Ozon").locator('..').locator('div > svg').click()
                            page.wait_for_timeout(1000)
                            page.locator("form path").nth(1).click()

                if ozon_page_num * len(data_jsons) == 0:
                    page.get_by_label("Поиск").click()
                    page.wait_for_load_state("domcontentloaded")
                    if len(page.locator('div[id^="state-searchResultsV2"]').all()) == 1:
                        data_json = json.loads(
                            page.locator('div[id^="state-searchResultsV2"]').all()[0].get_attribute('data-state'))
                        print(data_json.get('page'))
                        data_jsons.append(data_json)
                        data_page_list.append(data_json)
                    page.wait_for_timeout(1_000)
                if page.get_by_role("link", name=f"Дальше").count() == 1:
                    for data in data_page_list:
                        items = data.get('items')
                        for item in items:
                            if item.get('skuId') == product_id:
                                new_ozon_items.append(ozon_items[i])
                                flag = False
                                print('找到商品')
                                page.pause()
                    print('点击翻页')
                    ozon_page_num += 1
                    page.get_by_role('link', name=f'{ozon_page_num}', exact=True).scroll_into_view_if_needed()
                    page.wait_for_timeout(3_000)
                    data_page_list = []
                    page.get_by_role('link', name=f'{ozon_page_num}', exact=True).click()
                    page.wait_for_timeout(1_000)
                    if len(page.locator('div[id^="state-searchResultsV2"]').all()) == 1:
                        data_json = json.loads(
                            page.locator('div[id^="state-searchResultsV2"]').all()[0].get_attribute('data-state'))
                        print(data_json.get('page'))
                        data_jsons.append(data_json)
                        data_page_list.append(data_json)
                else:
                    page.wait_for_load_state("networkidle")
                    page.evaluate("window.scrollTo({top: window.scrollY + window.innerHeight, behavior: 'smooth'})")
        if keyword_txt == '':
            page.get_by_placeholder("Искать на Ozon").type(product_id, delay=267)
            # page.wait_for_load_state("networkidle")
            page.get_by_label("Поиск").click()
            page.wait_for_load_state("networkidle")
            # page.pause()
            if page.get_by_role("button", name="Добавить в корзину").is_visible():
                page.wait_for_timeout(7_00)
                cart_state = page.locator('div[id^="state-webAddToCart"]').all()[0].get_attribute('data-state')
                cart_item = json.loads(cart_state)
                if cart_item.get('sku', "") == product_id:
                    page.get_by_role("button", name="Добавить в корзину").click()
                    page.wait_for_timeout(7_00)
                    cart_price_str = cart_item.get('price', 0).replace(' ', '').replace(' ', '')
                    cart_price = int(cart_price_str.replace('\xa0', '').replace('₽', ''))
                    if ozon_items[i]['price'] < cart_price:
                        ozon_items[i]['price'] = cart_price
                    r_cart_price += cart_price
                    logging.info(f'{buyer} 商品:{product_id} 加入购物车 单价:{cart_price}₽')
                    new_ozon_items.append(ozon_items[i])
                    page.wait_for_timeout(7_00)
            elif len(page.locator('div[id^="state-webAddToCart"]').all()) > 1:
                cart_state = page.locator('div[id^="state-webAddToCart"]').all()[0].get_attribute('data-state')
                cart_item = json.loads(cart_state)
                cart_sku = cart_item.get('sku', "")
                cart_price_str = cart_item.get('price', 0).replace(' ', '').replace(' ', '')
                cart_price = int(cart_price_str.replace('\xa0', '').replace('₽', ''))
                if ozon_items[i]['price'] < cart_price:
                    ozon_items[i]['price'] = cart_price
                r_cart_price += cart_price
                cart_qyt = cart_item.get('quantity', 0)
                logging.info(f'{buyer} 商品:{product_id} 已在购物车 单价:{cart_price}₽')
                if cart_sku == product_id and cart_qyt > 0:
                    new_ozon_items.append(ozon_items[i])
                    continue
            elif len(page.locator('div[id^="state-searchResultsV2"]').all()) == 0:
                logging.warning(f'{buyer} 商品:{product_id} 被下架')
                page.locator("form path").nth(1).click()
                page.wait_for_timeout(7_00)
                continue
            elif len(page.locator('div[id^="state-searchResultsV2"]').all()) == 1:
                search_item = \
                    json.loads(
                        page.locator('div[id^="state-searchResultsV2"]').all()[0].get_attribute('data-state')).get(
                        'items')[0]
                if search_item.get('skuId') == product_id:
                    button_quantity = search_item.get('multiButton').get('ozonButton').get(
                        'addToCartButtonWithQuantity')
                    if button_quantity.get('maxItems') == 0:
                        logging.warning(f'{buyer} 商品:{product_id} 库存不足')
                        page.locator("form path").nth(1).click()
                        page.wait_for_timeout(7_00)
                        continue
                    if button_quantity.get('currentItems') >= 1:
                        logging.info(f'{buyer} 商品:{product_id} 已在购物车 单价:')
                        new_ozon_items.append(ozon_items[i])
                        page.locator("form path").nth(1).click()
                        page.wait_for_timeout(7_00)
                        continue
                    page.click(f'text={button_quantity.get("text")}')
                    logging.info(f'{buyer} 商品:{product_id} 加入购物车 单价:')
                    page.wait_for_load_state("networkidle")
                    new_ozon_items.append(ozon_items[i])
                    page.wait_for_timeout(1_000)
                    page.locator("form path").nth(1).click()
                    page.wait_for_timeout(7_00)

    ozon_items = new_ozon_items
    # 购物车
    if page.locator('div[data-widget="searchBarDesktop"] > div').count() == 2:
        page.mouse.click(2, 3)
        page.wait_for_timeout(7_00)
    page.goto("https://www.ozon.ru/cart", timeout=10000)  # 购物车
    page.wait_for_timeout(5000)
    page.get_by_role("button", name="Перейти к оформлению").click()  # 结算
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(2000)
    page.locator('div[style*="sberpay"]').click()
    page.wait_for_timeout(1000)
    page.get_by_role("button", name="Оплатить онлайн").click()  # 支付
    page.wait_for_timeout(10_000)
    # 查找特定元素
    page.screenshot(clip=page.locator("#root > div > div:nth-child(2)").bounding_box(),
                    path='C:/Users/<USER>/Downloads/fukuang.png')
    logging.info('等待付款200秒')
    page.wait_for_timeout(200_000)
    # 点击返回按钮

    page.get_by_role("button", name="Вернуться на сайт").click()
    # 等待URL变化 https://www.ozon.ru/my/order-done?orderNumber=0175716180-0004&showTabBar=false&number=0175716180-0004
    page.wait_for_timeout(10_000)
    order_id = [param.split('=')[1] for param in page.url.split('&')][0]
    order_done = page.locator('div[id^="state-orderDoneStatus"]').all()[0].get_attribute('data-state')
    money_str = json.loads(order_done).get('subtitle')[1].get('text', '')
    payment_amount = money_str.replace(' ', '').replace(' ', '').replace('\xa0', '').replace(' ', '').replace('₽', '')
    logging.info(f'{buyer} 订单号:{order_id} 下单数:{len(ozon_items)}')
    logging.info(f'预估金额:{budget_amount}₽ ,订单金额:{r_cart_price}₽ ,付款金额:{payment_amount}₽')
    for item in ozon_items:
        db['orders'].update_one({'taskId': item['taskId']}, {
            '$set': {'status': 3, 'orderId': order_id, 'price': item['price'], 'updateTime': datetime.now()}})

    # 当前时间到明天0点的还有多少秒
    seconds_left = (datetime.combine(datetime.now().date() + timedelta(days=1), time(0, 0, 0)) - datetime.now()).seconds
    r_db.set(f"{buyer}_ozon", 1, ex=seconds_left)
    return None


def action_get_code_and_review(client: HubStudioClient):
    # 筛选出有购买的账号,筛选条件,status=3或者status=4
    orders = db['orders'].find({'status': {'$in': [ 3]}})
    # buyers去重
    buyers = [order['buyer'] for order in orders]
    buyers = list(set(buyers))
    buyers = ["79104684025"]
    # todo 筛选出有评论的账号
    # buyers.append('79801812061')
    for buyer in buyers:
        if buyer is None:
            continue
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        # if db['pickup_info'].find_one({'platform': 'wb', 'buyer': buyer, 'date_at': {'$gte': today}}) is not None:
        #     logging.info(f'账号:{buyer},wb今天已经获取过取件码')
        #     continue
        ip.change(buyer)
        buyerInfo = BuyerInfo(buyer, db)
        browser, page, playwright = handle_browser(buyerInfo.device_id, client)
        if buyer == '79801812061' and not r_db.exists(f"{buyer}_order_pickup_wb"):
            get_wb_code_return(page, buyer)
            break
        is_wb_login(page, buyer)
        is_ozon_login(page, buyer)
        if not r_db.exists(f"{buyer}_order_pickup_ozon"):
            get_ozon_code(page, buyer)
            action_ozon_check_order_status(page, buyer)
            save_operate_key(buyer,"order_pickup_ozon")

        if not r_db.exists(f"{buyer}_order_pickup_wb"):
            get_wb_code(page, buyer)
            action_wb_check_order_status(page, buyer)
            save_operate_key(buyer, "order_pickup_wb")


        page.close()
        playwright.stop()
    logging.info('所有账号获取取件码完成')

def get_yandex_code(page, buyer):
    pass


def action_yandex_check_order_status(page, buyer):
    pass


def action_yandex_review(page, buyer, review_items):
    pass

# 保存 已操作的key
def save_operate_key(buyer, key):
    seconds_left = (datetime.combine(datetime.now().date() + timedelta(days=1),
                                     time(0, 0, 0)) - datetime.now()).seconds
    if buyer == key:
        r_db.set(f"{buyer}", 1, ex=seconds_left)
    else:
        r_db.set(f"{buyer}_{key}", 1, ex=seconds_left)
def action_workforce(client: HubStudioClient):
    # 获取当前日期
    current_date = datetime.now().date() - timedelta(days=3)
    pre_date = datetime.combine(current_date, time(0, 0, 0))

    # 查询订单
    orders = db['orders'].find(
        #{"status": {'$in': [1, 3, 4]}, 'buyer': '79104934761'}
        {"status": {'$in': [1, 3, 4]}, 'buyer': {'$exists': True, '$ne': ''}}
    ).sort('preDate', 1)

    # 按买家和平台分类订单
    order_task_items = {}
    for order in orders:
        buyer = order['buyer']
        platform = order.get('platform')
        status = order.get('status')

        if buyer not in order_task_items:
            order_task_items[buyer] = {}

        botms = order_task_items[buyer]
        task_key = f'order_{"shopping" if status == 1 else "pickup" if status == 3 else "review"}_{platform}'

        if task_key not in botms:
            botms[task_key] = []
        botms[task_key].append(order)

    # 处理每个买家的任务
    for buyer, task in order_task_items.items():
        if r_db.exists(buyer):
            logging.info(f'buyer:{buyer}已处理，跳过')
            continue
        # 更换IP
        ip.change(buyer)
        buyer_info = BuyerInfo(buyer, db)
        # 初始化浏览器
        browser, page, playwright = handle_browser(buyer_info.device_id, client)

        is_wb_login(page, buyer)
        is_ozon_login(page, buyer)

        if task.get('order_pickup_wb') and not r_db.exists(f"{buyer}_order_pickup_wb"):
            get_wb_code(page, buyer)
            save_operate_key(buyer, 'order_pickup_wb')
        if task.get('order_pickup_ozon') and not r_db.exists(f"{buyer}_order_pickup_ozon"):
            get_ozon_code(page, buyer)
            save_operate_key(buyer, 'order_pickup_ozon')
        if task.get('order_pickup_yandex') and not r_db.exists(f"{buyer}_order_pickup_yandex"):
            get_yandex_code(page, buyer)
            save_operate_key(buyer, 'order_pickup_yandex')
        # 处理购物任务
        for platform in ['wb', 'ozon', 'yandex']:
            shopping_key = f'order_shopping_{platform}'
            if task.get(shopping_key):
                items = task[shopping_key]
                # 随机采样
                sample_size = max(1, int(len(items) * 0.7)) if len(items) > 3 else \
                    random.choices([1, 2], weights=[0.3, 0.7])[0] if len(items) >= 2 else 1
                sampled_items = random.sample(items, min(sample_size, len(items)))

                logging.info(f'开始{platform.upper()}购物 订单数量:{len(sampled_items)}')
                # 检查是否已购物
                if platform == 'wb' and not r_db.exists(f"{buyer}_{shopping_key}"):
                    buy_wb_item(sampled_items, buyer, page)
                    save_operate_key(buyer, shopping_key)
                elif platform == 'ozon' and not r_db.exists(f"{buyer}_{shopping_key}"):
                    buy_ozon_item(sampled_items, buyer, page)
                    save_operate_key(buyer, shopping_key)
                elif platform == 'yandex' and not r_db.exists(f"{buyer}_{shopping_key}"):
                    buy_yandex_item(sampled_items, buyer, page)
                    save_operate_key(buyer, shopping_key)


        # 处理评论任务
        for platform in ['wb', 'ozon', 'yandex']:
            review_key = f'order_review_{platform}'
            if task.get(review_key):
                review_items = None
                if platform == 'wb' and not r_db.exists(f"{buyer}_{review_key}"):
                    review_items = action_wb_check_order_status(page, buyer)
                elif platform == 'ozon' and not r_db.exists(f"{buyer}_{review_key}"):
                    review_items = action_ozon_check_order_status(page, buyer)
                elif platform == 'yandex' and not r_db.exists(f"{buyer}_{review_key}"):
                    review_items = action_yandex_check_order_status(page, buyer)

                if review_items:
                    num_to_review = random.randint(1, min(5, len(review_items)))
                    review_items = random.sample(review_items, num_to_review)
                    logging.info(f'准备{platform.upper()}评论{len(review_items)}条订单')
                    if platform == 'wb' and not r_db.exists(f"{buyer}_{review_key}"):
                        action_wb_review(page, buyer, review_items)
                        save_operate_key(buyer, review_key)
                    elif platform == 'ozon' and not r_db.exists(f"{buyer}_{review_key}"):
                        action_ozon_review1(page, buyer, review_items)
                        save_operate_key(buyer, review_key)
                    elif platform == 'yandex' and not r_db.exists(f"{buyer}_{review_key}"):
                        action_yandex_review(page, buyer, review_items)
                        save_operate_key(buyer, review_key)

        save_operate_key(buyer, buyer)
        page.close()
        playwright.stop()



def main():
    proxy = ProxyServer("WLAN2", 1081)
    proxy.start()
    # 获取默认配置文件路径
    config_path = get_default_config_path()
    # 加载配置文件
    config = load_config(config_path)
    # 使用配置文件方式初始化客户端
    hs_client = HubStudioClient(config=config)
    try:
        # action_get_code_and_review(hs_client)
        action_workforce(hs_client)
    except HubStudioError as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭客户端连接
        hs_client.stop()
        # 停止所有代理服务
        # proxy_manager.stop_all()
        proxy.stop()


def handle_browser(container_code: int, client: HubStudioClient):
    """处理浏览器自动化任务"""
    try:
        # 使用start_playwright替代open_browser
        browser_info, browser, playwright = client.start_playwright(container_code, args=['--start-maximized'])
        # 不需要使用with语句，直接使用返回的browser对象
        page = browser.contexts[0].pages[0]
        return browser, page, playwright

    except Exception as e:
        print(f"操作浏览器时发生错误: {str(e)}")


if __name__ == '__main__':
    # 执行随机评论功能
    # random_wb_review()
    # 或者执行主功能
    main()
