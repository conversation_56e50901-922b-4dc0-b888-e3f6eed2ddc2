
def ozon_login(page, buyer):
    page.locator('input.input-item').clear()
    page.locator('input.input-item').fill(buyer)
    page.click('button:has-text("Получить код")')
    el = page.wait_for_selector('button:has-text("Запросить код на телефон")', state='visible', timeout=63000)
    if el is not None:
        el.click()
        el = None
    el = page.wait_for_selector('.form-block__captcha-img', state='visible', timeout=30000)  # 验证码
    if el is not None:
        code = input()
        page.locator("input#smsCaptchaCode").fill(code, timeout=854)
        page.wait_for_selector('input.j-b-charinput').click()
        smscode = input()
        # 键盘输入smscode
        for k in smscode:
            page.keyboard.press(k, delay=243)
        page.wait_for_timeout(3000)
        if page.url == 'https://www.wildberries.ru/lk/basket':
            page.goto('https://www.wildberries.ru/lk/myorders/archive')
        page.wait_for_timeout(3000)