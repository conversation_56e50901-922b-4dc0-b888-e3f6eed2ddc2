import json
import socket
import time
import pywifi
import psutil
import redis
import requests
from lendian import logger
from lendian.api import LDApi
from requests_toolbelt.adapters import source

class ChangeIP:
    def __init__(self, r_db: redis.Redis|None,adb: LDApi|None):
        if r_db is None:
            self.r_db = redis.Redis(host='localhost', port=6379, db=0,decode_responses=True)
        else:
            self.r_db = r_db

        if adb is None:
            self.adbc = LDApi(r'D:\leidian\LDPlayer9').adb
        else:
            self.adbc = adb
        self.iface_usb_name = {'iface_name': 'Realtek 8811CU Wireless LAN 802.11ac USB NIC', 'wifi': 'WI-FI'}
        self.iface_pci_name = {'iface_name': 'Realtek 8821CE Wireless LAN 802.11ac PCI-E NIC', 'wifi': 'WLAN2'}
        self.device = 'Q7PRX18C10003990'
        self.wait_time = 40  #默认等待时间40秒
        self.interfaces = pywifi.PyWiFi().interfaces()  #获取PC机所有无线网卡
        self.profile = pywifi.Profile()  #初始化连接wifi
        self.profile.ssid = 'Honor 10 Lite'

    def change(self,client_id):
        # 触发换IP 事件
        if self.r_db.get('last_client') == client_id:
            logger.info('无需切换IP')
            return
        self._switch_wifi()
        self.r_db.set('last_client', client_id)
        return

    def _switch_wifi(self) -> bool:
        logger.info('开始操作真机切换WIFI')
        self.adbc.swipe(self.device, 890, 36, 890, 467, 500)  # 下拉
        while True:
            if self.adbc.get_airplane_mode_state(self.device) == '1':  # 判断是否是开启飞行模式
                self.adbc.click(self.device, 194, 1218)  # 点击关闭飞行模式
                time.sleep(0.3)
                self.adbc.click(self.device, 194, 1510)  # 点击开启热点
                logger.info('真机已关闭飞行模式并打开热点')
                logger.info('操作连接真机WIFI')
                if self._link_wifi():
                    for i in range(15):  # 设置重试次数
                        try:
                            logger.info('测试是否连通网络，等待{}秒'.format(i+1))
                            time.sleep(i+1)
                            new_ip = self._get_ip(self.iface_pci_name['wifi'])
                            break  # 成功运行后退出循环
                        except Exception as error:
                            if i != 0:
                                print(f"Run failed, retrying. Error: {error}")
                        if i < 15:  # 如果还有剩余尝试次数，继续尝试
                            continue
                        else:
                            raise error  # 超过最大重试次数，抛出异常
                    logger.info('真机当前IP:{}'.format(new_ip))
                    # #判断网络是否可用
                    # result = ping("************")
                    # if result != None:
                    #     logger.info('************连接成功')
                    # else:
                    #     logger.info('************连接失败')
                    #     raise Exception('************连接失败')

                    if self.r_db.exists(new_ip):  # IP相同
                        self.adbc.click(self.device, 194, 1218)  # 点击开启飞行模式
                        logger.debug('IP未变化请等待{}秒再次切换'.format(40))
                        time.sleep(40)
                    else:
                        self.r_db.set(new_ip, 0,3600) #3小时后过期
                        logger.info('成功切换IP:{}'.format(new_ip))
                        self.adbc.input_key(self.device, 'BACK')
                        return True
            else: #未开启飞行模式
                self.adbc.click(self.device, 194, 1218)  # 点击开启飞行模式
                logger.info('真机已开启飞行模式')
                logger.debug('请等待{}秒后切换'.format(self.wait_time))
                time.sleep(self.wait_time)

    def _get_ip(self, wifi_name: str):
        net_if_addrs = psutil.net_if_addrs()  #获取本地网卡信息
        iface_ip = None
        if wifi_name in net_if_addrs:
            # 获取该网卡的所有IPv4
            for iface in net_if_addrs[wifi_name]:
                if iface.family == socket.AF_INET:
                    iface_ip = iface.address
        if iface_ip is not None:
            s = requests.session()
            s.headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }
            new_s = source.SourceAddressAdapter(iface_ip)
            s.mount('http://', new_s)
            s.mount('https://', new_s)
            return s.get('http://*************/ip').text

    # 链接wifi
    def _link_wifi(self):
        iface = None
        for temp in self.interfaces:
            if temp.name() == self.iface_pci_name['iface_name']:
                iface = temp
        if iface is None:
            logger.info('找不到USB网卡')
            return False
        if iface.status() == 4:
            return True

        iface.connect(self.profile)  # 通过添加的Profile 连接指定wifi
        time.sleep(10)  # 休眠10s
        while True:
            if iface.status() == 4:
                return True
            else:
                logger.info('无法链接手机热点，重新检测5秒')
                time.sleep(5)  # 休眠5s


if __name__ == '__main__':
    r_db = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    ld_sdk = LDApi(r'D:\leidian\LDPlayer9')  # 链接雷电模拟器驱动
    ip = ChangeIP(r_db, ld_sdk.adb)
    ip.change('79104934896')
