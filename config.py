import os
import json
from typing import Dict, Any

class Config:
    def __init__(self):
        self.config_path = self._get_default_config_path()
        self.config = self._load_config()
        
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        return os.path.join(os.path.dirname(__file__), 'config.json')
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
            
    def get_proxy_config(self) -> Dict[str, str]:
        """获取代理配置"""
        return self.config.get('proxy', {})
        
    def get_redis_config(self) -> Dict[str, Any]:
        """获取Redis配置"""
        return self.config.get('redis', {})
        
    def get_mongodb_config(self) -> Dict[str, Any]:
        """获取MongoDB配置"""
        return self.config.get('mongodb', {}) 