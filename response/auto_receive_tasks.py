#!/usr/bin/python
# -*- coding: UTF-8 -*-
import random
import pymongo
from handler.PlaywrightHandler import *

from info import BuyerInfo,InfoTask,ShuadanTask,ReviewTask,db,DailyMaintenanceTask
from browser.start_browser import open_browser
from functools import wraps



def client_main(func):
    def outwrapper(*args, **kwargs):
        print("======================<start>=========================")
        taskId = kwargs.get('taskId')
        taskDB = DB_control(taskId)
        taskInfo = taskDB.findTaskInfo()
        ip_port = taskInfo.buyer
        buyerInfo = BuyerInfo(ip_port)

        page, close_client, playwright, browser, close_env ,browser_context= open_browser(buyerInfo)
        # #cookies备份
        # storage = browser_context.storage_state()
        # with open("{}2.json".format(taskInfo.buyer), "w") as f:
        #     f.write(json.dumps(storage))

        print(buyerInfo.email)
        try:
            # client = Page_action()
            client = PageShuadanActionBase(page,browser_context, buyerInfo,taskDB,db)

            if not client.check_login():
                raise '账号检查，登陆异常'
            kwargs.update({'client':client,'taskInfo':taskInfo})
            return func(*args,**kwargs)
        except Exception as e:
            print('【出错了-{}】'.format(buyerInfo.device_id))
            print(e)
        finally:


            browser_context.close()
            browser.close()
            time.sleep(1)
            playwright.stop()
            close_env(container_code=buyerInfo.device_id)
            close_client()
            print("======================<end>=========================")

    return outwrapper

def client_day(func):
    def outwrapper(*args, **kwargs):
        print("======================<start>=========================")

        taskDB = DB_control(None)

        taskInfo = taskDB.findDailyMaintenanceTask()
        taskDB = DB_control(taskInfo.taskId)
        ip_port = taskInfo.buyer
        buyerInfo = BuyerInfo(ip_port)
        page, close_client, playwright, browser, close_env ,browser_context= open_browser(buyerInfo)
        #cookies备份
        storage = browser_context.storage_state()

        with open("{}.json".format(taskInfo.buyer), "w") as f:
            f.write(json.dumps(storage))


        print(buyerInfo.email)
        try:
            # client = Page_action()
            client = PageShuadanActionBase(page,browser_context, buyerInfo,taskDB,db)

            if not client.check_login():
                raise '账号检查，登陆异常'
            kwargs.update({'client':client,'taskInfo':taskInfo})
            return func(*args,**kwargs)
        except Exception as e:
            print('【出错了-{}】'.format(buyerInfo.device_id))
            print(e)
        finally:
            storage = browser_context.storage_state()
            with open("{}_end.json".format(taskInfo.buyer), "w") as f:
                f.write(json.dumps(storage))
            browser.close()
            time.sleep(1)
            playwright.stop()
            close_env(container_code=buyerInfo.device_id)
            close_client()
            print("======================<end>=========================")

    return outwrapper


def run(func,value):
   return func(*value)




def register_account(page,buyerInfo):
    client = Register(page,buyerInfo)
    client.finish_phoneNum_login()
    client.binging_email()
    print(client)


def funatt_decorator(funatt_dict):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            client = Page_action(*args, **kwargs)
            for key, value in funatt_dict.items():
                if key == 'delete_cart_product':
                    getattr(client, key)(*value)
                elif key == 'action_item':
                    getattr(client, key)(**value)
            return func(*args, **kwargs)
        return wrapper
    return decorator






class DB_control:
    def __init__(self,taskId):
        self.taskId = taskId

    def findDailyMaintenanceTask(self):
        taskInfo = db.sd_dailyMaintenanceTask.find_one({'isDone':'FALSE'})
        taskInfo = DailyMaintenanceTask(taskInfo)
        return taskInfo

    def findOzonKeyword(self):
        # 使用 $sample 聚合管道操作符随机选择一条文档
        pipeline = [{"$sample": {"size": 1}}]
        result = list(db.ozon_keyword.aggregate(pipeline))
        return result[0]

    def addDailyMaintenanceTask(self,keyword,buyer):
        preDateTime = datetime.datetime.now()
        taskInfo = db.sd_dailyMaintenanceTask.insert_one({"taskId": self.taskId,'keyword':keyword,'buyer':buyer,'preDateTime':preDateTime,'isDone':'FALSE','doneTime':''})
        return taskInfo

    def findTaskInfo(self):
        taskInfo =  db.sd_infoTask.find_one({"taskId":self.taskId})
        taskInfo = InfoTask(taskInfo)
        return taskInfo

    def findShuadanTask(self):
        shuadanTask = db.sd_todoShuadanTask.find_one({"taskId":self.taskId})
        shuadanTask = ShuadanTask(shuadanTask)
        return shuadanTask

    def findReviewTask(self):
        reviewTask = db.sd_todoReviewTask.find_one({"taskId":self.taskId})
        reviewTask = ReviewTask(reviewTask)
        return reviewTask

    def get_shuadan_task(self):
        shuadanTaks = db.sd_todoShuadanTask.find_one({"taskId":self.taskId})

    def modify_shuadanTask_done(self,method):
        t = datetime.datetime.now()
        methodTime = method+'_time'
        db['sd_todoShuadanTask'].update_one({"taskId":self.taskId},{"$set": {method: "DONE",methodTime:t}})


    def modify_shuadanTask_error(self,method):
        db['sd_todoShuadanTask'].update_one({"taskId":self.taskId},{"$set": {method: "ERROR"}})


    def modify_infoTask_orderId(self,orderId):
        db['sd_infoTask'].update_one({"taskId": self.taskId}, {"$set": {'orderId':orderId}})
        db['sd_dailyMaintenanceTask'].update_one({"taskId": self.taskId}, {"$set": {'orderId':orderId}})

    def modify_infoTask_payInfo(self,price):
        db['sd_infoTask'].update_one({"taskId": self.taskId}, {"$set": {'price':price}})

    def modify_shuadanTask_unlogin(self):
        data = db['sd_todoShuadanTask'].find_one({"taskId": self.taskId})
        updata ={}
        if data.get("like") == "TRUE":
            updata.update({"like": "UNLOGIN"})
        if data.get("qa") == "TRUE":
            updata.update({"qa": "UNLOGIN"})
        if data.get("cart") == "TRUE":
            updata.update({"cart": "UNLOGIN"})
        if data.get("order") == "TRUE":
            updata.update({"order": "UNLOGIN"})
        if data.get("pay") == "TRUE":
            updata.update({"pay": "UNLOGIN"})

        db['sd_todoShuadanTask'].update_one({"taskId": self.taskId}, {"$set": updata})

    def modify_DailyMaintenanceTask_done(self,shopName = ''):
        t = datetime.datetime.now()
        db['sd_dailyMaintenanceTask'].update_one({"taskId":self.taskId},{"$set": {'isDone': "DONE",'doneTime':t,'shopName':shopName}})

    def modify_DailyMaintenanceTask_error(self):
        db['sd_dailyMaintenanceTask'].update_one({"taskId":self.taskId},{"$set": {'isDone': "ERROR"}})

@client_main
def action_shuadan(**kwargs):
    taskInfo = kwargs.get('taskInfo')
    client: PageShuadanActionBase = kwargs.get('client')
    client.taskInfo = taskInfo
    #点赞
    if kwargs.get('like')== True:
        func = getattr(client, 'like')
        func_value = taskInfo
        func(func_value)

    #问答
    if kwargs.get('qa') == True:
        func = getattr(client, 'qa')
        func_value = taskInfo
        func(func_value)
    #加入购物车
    if kwargs.get('cart') == True:
        func = getattr(client, 'cart')
        func_value = taskInfo
        func(func_value)
    #下单
    if kwargs['cart'] == 'DONE' and kwargs['order'] == True:
        func = getattr(client, 'order')
        func_value = taskInfo
        func(func_value)
    #付款
    if kwargs['order'] == 'DONE' and kwargs['pay'] == True:
        func = getattr(client, 'pay')
        func_value = taskInfo
        func(func_value)



@client_day
def daily_maintenance(**kwargs):
    taskInfo = kwargs.get('taskInfo')
    client: PageShuadanActionBase = kwargs.get('client')
    client.taskInfo = taskInfo
    client.click_item(taskInfo)




def action_reviewTask(client: Page_action, **kwargs):
    task = DB_control(kwargs['taskId'])
    reviewTask: ReviewTask = task.findReviewTask()
    if kwargs['todu']['review'] == True:
        func = getattr(client, 'review')
        func_value = reviewTask
        run(func, func_value)


def creat_daily_maintenance():
    for i in range(7110,7130):
        buyer = str(i)
        timestamp = time.time()
        int_timestamp = int(timestamp)
        taskId = 'dm-'+str(int_timestamp)
        taskDB = DB_control(taskId)
        keyword = taskDB.findOzonKeyword()["俄语搜索词"]
        taskDB.addDailyMaintenanceTask(keyword,buyer)
        time.sleep(2)

def creat_daily_maintenance_case():
    for i in range(7115,7152):
        keyword_case_list=[
            'Магнитный противоударный иликоновый чехол на iphone 11',
            'Иликоновый противоударный чехол на iPhone 14 Pro Max',
            'Чехол на iPhone 11 с ошторкой, в стиле милитари, противоударный',
            'Чехлы с рисунками на iPhone 11 противоударный',

        ]
        buyer = str(i)
        timestamp = time.time()
        int_timestamp = int(timestamp)
        taskId = 'dm-'+str(int_timestamp)
        taskDB = DB_control(taskId)
        keyword = random.choice(keyword_case_list)
        taskDB.addDailyMaintenanceTask(keyword,buyer)
        time.sleep(2)


def count_words_at_url(url):
    """Just an example function that's called async."""
    resp = requests.get(url)
    return len(resp.text.split())

