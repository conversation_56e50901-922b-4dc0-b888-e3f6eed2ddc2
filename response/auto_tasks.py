import datetime
import time

# import redis
# from rq import Queue
from auto_receive_tasks import db,DailyMaintenanceTask,daily_maintenance,ShuadanTask,action_shuadan,count_words_at_url
# import multiprocessing
# redis_conn = redis.Redis(host="************", port=6379, db=6, password="AirSmile@1688", max_connections=40)
# q = Queue(connection=redis_conn)

def get_daily_maintenance():
    taskInfo = db['sd_dailyMaintenanceTask'].find_one({"isDone": "FALSE"})
    if taskInfo:

        taskInfo = DailyMaintenanceTask(taskInfo)
        print('每日维护：', taskInfo.taskId, taskInfo.buyer)
        if taskInfo.isDone == 'FALSE':
            taskInfo.isDone = False
            # 添加任务
            daily_maintenance(client='', taskInfo=taskInfo)



def get_task():
    current_time = datetime.datetime.now()
    # 构造查询条件
    query ={'preDateTime':{"$lt": current_time},"$or": [{"like": "TRUE"},{"qa": "TRUE"},{"cart": "TRUE"},{"order": "TRUE"},{"order": "DONE",'pay':"TRUE"}]}
    task = db['sd_todoShuadanTask'].find_one(query)
    if not task:
        query = {'preDateTime': {"$lt": current_time}, "order": "DONE", 'pay': "TRUE"}
        task = db['sd_todoShuadanTask'].find_one(query)
    if task:
        task = ShuadanTask(task)
        if task.like == "TRUE":
            task.like = True
        elif task.like == "FALSE":
            task.like = False

        if task.qa == "TRUE":
            task.qa = True
        elif task.qa == "FALSE":
            task.qa = False

        if task.cart == "TRUE":
            task.cart = True
        elif task.cart == "FALSE":
            task.cart = False

        if task.order == "TRUE":
            task.order = True
        elif task.order == "FALSE":
            task.order = False

        if task.order == "DONE":
            if task.pay == "TRUE":
                task.pay = True
            elif task.pay == "FALSE":
                task.pay = False

        # 添加任务
        print('刷单执行：', task.taskId,)
        action_shuadan(client= '', taskId=task.taskId,like=task.like,qa=task.qa,cart=task.cart,order=task.order,pay=task.pay,)



if __name__ == '__main__':
    while True:
        # get_daily_maintenance()
        get_task()
        print('等待任务中。。')
        time.sleep(10)
