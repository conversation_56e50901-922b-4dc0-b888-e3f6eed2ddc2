#!/usr/bin/python
# -*- coding: UTF-8 -*-

class EnvCreateModel:
    """创建环境"""

    # 使用方式 1静态 2动态
    asDynamicType = None

    # 环境名称
    containerName = None

    # 代理帐号
    proxyAccount = None

    # 代理密码
    proxyPassword = None

    # 代理端口
    proxyPort = None

    # 代理主机
    proxyServer = None

    # 代理类型
    proxyTypeName = None

    # 备注
    remark = None

    # 分组
    tagName = None

    # cookie
    cookie = None

    # 参考城市
    referenceCity = None

    # 参考国家代码
    referenceCountryCode = None

    # 参考州
    referenceRegionCode = None
