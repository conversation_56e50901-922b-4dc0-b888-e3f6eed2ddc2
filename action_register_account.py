import json
import os
import re
from datetime import datetime

import redis
from playwright.sync_api import sync_playwright

from browser.start_browser import open_browser
from change_ip import ChangeIP
from db.client_mongdb import login_mongdb_review
from info import BuyerInfo
from lendian.api import LDApi


class ActionRegisterAccount:
    def __init__(self):
        self.page = None
        self.close_client = None
        self.playwright = None
        self.browser = None
        self.close_env = None
        self.context = None
        self.m_db = login_mongdb_review()
        self.buyer_info = None
        self.r_db = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        self.ld_sdk = LDApi(r'D:\leidian\LDPlayer9')  # 链接雷电模拟器驱动
        self.ip = ChangeIP(self.r_db, self.ld_sdk.adb)

    def get_buyer_info(self,phone):
        self.buyer_info = BuyerInfo(phone, self.m_db)
        self.ip.change(phone)

    def start_browser(self):
        self.page, self.close_client, self.playwright, self.browser, self.close_env, self.context = open_browser(self.buyer_info)

    def save_cookies(self):
        file_path = './cookies/' + self.buyer_info.phone + '.json'
        with open(file_path, "w") as f:
            f.write(json.dumps(self.context.cookies()))
        f.close()

    def load_cookies(self):
        file_path = './cookies/' + self.buyer_info.phone + '.json'
        if os.path.exists(file_path):
            with open(file_path, "r") as f:
                cookies = json.loads(f.read())
            f.close()
            self.context.add_cookies(cookies)
        else:
            self.page.pause()

    def open_yandex_page(self):
        mail_page = self.context.new_page()
        wb_page = self.context.new_page()
        yd_page = self.context.new_page()
        # tg_page = self.context.new_page()
        mail_page.goto("https://www.mail.ru/",wait_until="load",timeout=120_000)
        self.page.goto("https://www.ozon.ru/",wait_until="load",timeout=120_000)
        wb_page.goto("https://www.wildberries.ru/",wait_until="load",timeout=120_000)
        yd_page.goto("https://market.yandex.ru/",wait_until="load",timeout=120_000)
        # tg_page.goto("https://web.telegram.org/")
        self.page.pause()
        mail_page.locator('#ph-whiteline > button').click()

    def run(self):
        self.get_buyer_info(phone='***********')
        self.start_browser()
        self.load_cookies()
        self.open_yandex_page()
        self.save_cookies()
        self.browser.close()


if __name__ == "__main__":
    scraper = ActionRegisterAccount()
    scraper.run()
