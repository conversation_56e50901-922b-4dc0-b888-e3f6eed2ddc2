import json
import logging
import random
import re
import time
from datetime import datetime, timedelta
from typing import Callable

import redis

from browser.start_browser import open_browser
from change_ip import ChangeIP
from common.ExchangeRate import ExchangeRate
from db.client_mongdb import login_mongdb_review
from info import BuyerInfo
from lendian import logger


m_db = login_mongdb_review()
r_db = redis.Redis(host='************', port=6379, db=1, password="Lens@9182", )
ex_rate = ExchangeRate(r_db)


def mark_settle_order(data):
    # 判断是否已经存在任务号的结算记录
    settle_record = m_db['settlement'].find_one({'taskId': data['taskId']}, {'_id': 0})
    if settle_record:
        print('结算记录已存在, taskId: {}'.format(data['taskId']))
        return None

    # 在数据库中查找data['taskId']这个任务号对应的订单信息
    order = m_db['orders'].find_one({'taskId': data['taskId']}, {'_id': 0})
    # 根据订单信息中的用户信息,在数据库中查找data['taskId']这个任务号对应的订单信息,用于获取计算结算价格
    userInfo = m_db['userinfo'].find_one({'userId': order['userId']}, {'_id': 0})

    rate = ex_rate.get_rate()
    if rate == 0 or rate is None:
        print('汇率获取失败, 请检查网络连接')
        return '汇率获取失败, 请检查网络连接'
    logging.info('汇率为: %s', rate)
    date_str = datetime.now().strftime("%Y-%m-%d")
    if order['orderPcs'] > 0:
        esCost = round(data['price'] * rate / 100, 2)
        orderPcs = userInfo['info'].get('PURCHASE_ORDER_BASE_PRICE')
        like = 0 if order['like'] == 0 else userInfo['info'].get('PURCHASE_ORDER_LIKE_PRICE')
        cart = 0 if order['cart'] == 0 else userInfo['info'].get('PURCHASE_ORDER_ADD_ON_PRICE')
        review = 0 if order['review'] == 0 else userInfo['info'].get('PURCHASE_ORDER_REVIEW_PRICE')
        espay = (like + cart + review + orderPcs) * userInfo['info'].get('FEE_DISCOUNT')
    else:
        esCost = 0
        orderPcs = 0
        like = 0 if order['like'] == 0 else userInfo['info'].get( 'UNPURCHASED_ORDER_ONLY_LIKE_PRICE')
        cart = 0 if order['cart'] == 0 else userInfo['info'].get('UNPURCHASED_ORDER_ONLY_ADD_ON_PRICE')
        review = 0
        espay = (like + cart + review + orderPcs) * userInfo['info'].get('FEE_DISCOUNT')

    data['esPay'] = round(espay, 2)
    data['esCost'] = round(esCost, 2)

    data2 = {
        'updateTime': datetime.now(),
        'status': 3,
        'price': round(data['price'] / order['orderPcs'], 2),  # 修正真是付款单价
        'esCost': round(esCost, 2),
        # 'orderId': data.get('orderId'),
        'buyer': data.get('buyer'),
    }
    print('更新任务号', data['taskId'], data2)

    # 更新订单状态
    m_db['orders'].update_one({'taskId': data['taskId']}, {"$set": data2})

    # 添加结算记录,在数据库settlement中
    ##先根据字段createDate的日期值,查询最新的结算信息
    settle_date_latest = m_db['settlement'].find({'userId': order['userId']}, {'_id': 0}).sort(
        [('createDate', -1)]).limit(1)
    settle_date_latest = settle_date_latest[0]
    ##余额为settle_date_latest['balance']
    balance = settle_date_latest['balance']
    ##生成结算号,结算号后四位为当前时间戳,时间戳精确到毫秒,取后8位
    t = int(time.time() * 1000)
    if esCost > 0:
        balance = round(balance - esCost, 2)
        settle_data_goods = {
            'taskId': data['taskId'],
            'settlementId': 'O' + order['userId'] + str(t + 1)[-8:],
            'createDate': datetime.now(),
            'itemType': '货款',
            'amount': round(-esCost, 2),
            'balance': balance,
            'userId': order['userId'],
        }
        print('添加货款结算记录', data['taskId'], settle_data_goods)
        # 添加结算记录
        result_goods = m_db['settlement'].insert_one(settle_data_goods)

    if espay > 0:
        balance = round(balance - espay, 2)
        settle_data_commission = {
            'taskId': data['taskId'],
            'settlementId': 'O' + order['userId'] + str(t + 1)[-8:],
            'createDate': datetime.now(),
            'itemType': '佣金',
            'amount': round(-espay, 2),
            'balance': balance,
            'userId': order['userId'],
        }
        print('添加佣金结算记录', data['taskId'], settle_data_commission)
        result_commission = m_db['settlement'].insert_one(settle_data_commission)
    result_data = [data2, settle_data_goods, settle_data_commission]

    return str(result_data)


def dispatcher():
    pre_buyers = [
        '79104684025',
        '79801811276',
        '79801813447',
        '79104980323',
        '79104981586',
        '79104981159',
        '79104981490',
        '79104981480',
        '79104981485',
        '79801812446',
        '79801814044',
        '79104073560',
        '79801813966',
        '79104981533',
        '79801814479',
        '79801815917',
        '79801811795',
        '79104981269',
        '79104981386',
        '79104981335',
        '79104683924',
        '79104934738',
        '79104684832',
        '79104934896',
        '79153391734',
        '79153390838',
        '79153379798',
        '79153379721',
        '79153379558',
        '79153379924',
        '79153380271',
        '79153379583',
        '79153379342',
        '79153379958',
        '79153380358',
        '79153391201',
        '79153379501',
        '79153379610',
        '79153380298',
        '79153391208',
        '79153390802',
        '79153390849',
        '79153390854',
        '79153391136',
        '79153379476',
        '79104934761',
        '79153379453',
    ]
    random.shuffle(pre_buyers)
    orders = []
    buyers_orders = {}
    pre_orders = m_db['orders'].find({"$or": [
        {"buyer": ""},
        {"buyer": None},  # 假设在MongoDB中表示为null
        {"buyer": {"$exists": False}}  # 字段不存在
    ],
        "status": 1},
        {'_id': 0, 'sellerId': 1, 'price': 1, 'buyer': 1, 'productId': 1, 'taskId': 1, 'platform': 1}).sort('platform')
    pre_orders = list(pre_orders)

    for buyer in pre_buyers:
        buyers_orders[buyer] = []
        # 查询该买家的所有订单记录
        last_oerders = m_db['orders'].find(
            {"buyer": buyer, 'status': {"$gte": 1}},
            {'_id': 0, 'sellerId': 1, 'buyer': 1, 'price': 1, 'productId': 1, 'taskId': 1, 'createTime': 1})
        last_oerders = list(last_oerders)

        # 创建一个已购买产品ID的集合，用于WB平台判断
        purchased_product_ids = {orders.get('productId') for orders in last_oerders}
        
        # 当前分配给买家的WB平台产品ID集合，用于限制单次分配数量
        current_wb_product_ids = set()
        
        # 随机选择WB平台每次最多分配的产品数量（10-15之间）
        max_wb_products = random.randint(10, 15)

        # 创建30天前的时间点，用于OZON平台判断
        thirty_days_ago = datetime.now() - timedelta(days=37)

        # 遍历待分配的订单
        for pre_order in pre_orders[:]:  # 使用切片创建副本进行遍历
            if pre_order.get('buyer'):  # 跳过已分配买家的订单
                continue

            if pre_order['platform'] == 'wb':
                # WB平台：判断是否购买过或分配过该产品，以及当前分配数量是否已达上限
                if (pre_order['productId'] not in purchased_product_ids and 
                    pre_order['productId'] not in current_wb_product_ids and 
                    len(current_wb_product_ids) < max_wb_products):
                    pre_order['buyer'] = buyer
                    buyers_orders[buyer].append(pre_order)
                    orders.append(pre_order)
                    pre_orders.remove(pre_order)  # 从待分配订单中移除
                    purchased_product_ids.add(pre_order['productId'])  # 更新已购买产品集合
                    current_wb_product_ids.add(pre_order['productId'])  # 更新当前分配的WB产品集合

            elif pre_order['platform'] == 'ozon':
                # OZON平台：判断30天内是否有订单
                ozon_recent_order = False
                for last_order in last_oerders:
                    # 检查是否有OZON平台的近期订单
                    if last_order.get('platform') == 'ozon' and last_order.get('createTime') and last_order[
                        'createTime'] > thirty_days_ago:
                        ozon_recent_order = True
                        break

                # 如果30天内没有OZON订单，则分配给该买家
                if not ozon_recent_order and pre_order['productId'] not in purchased_product_ids:
                    pre_order['buyer'] = buyer
                    buyers_orders[buyer].append(pre_order)
                    orders.append(pre_order)
                    pre_orders.remove(pre_order)  # 从待分配订单中移除
                    purchased_product_ids.add(pre_order['productId'])  # 更新已购买产品集合

    return buyers_orders



def jiesuan():
    pre_orders = m_db['orders'].find(
        {"status": {"$in": [3]}},
        {'_id': 0, 'sellerId': 1, 'price': 1, 'buyer': 1, 'productId': 1, 'taskId': 1, 'platform': 1})
    for order in pre_orders:
        mark_settle_order(order)

def fenpei():
    items = dispatcher()
    for buyer, item_list in items.items():
        if len(item_list) == 0:
            continue
        logger.info('买手: %s', buyer)
        wb_prices = 0
        ozon_prices = 0
        yandex_prices = 0
        for item in item_list:
            if item['platform'] == 'wb':
                wb_prices += item['price']
            if item['platform'] == 'ozon':
                ozon_prices += item['price']
            if item['platform'] == 'yandex':
                yandex_prices += item['price']
            m_db['orders'].update_one({'taskId': item['taskId']}, {'$set': {'buyer': buyer, 'orderId': ""}})
        logger.info('WB总价:%s', wb_prices)
        logger.info('OZON总价:%s', ozon_prices)
        logger.info('Yandex总价:%s', yandex_prices)
        logger.info('================================================')

jiesuan()
# buy_shop_item()
#fenpei()
