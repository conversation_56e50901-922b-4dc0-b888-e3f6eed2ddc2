from redis import Redis
red = Redis(host='************', port=6379, db=4, password="AirSmile@ali", )
red3 = Redis(host='************', port=6379, db=3, password="AirSmile@ali", )
import platform

if platform.system() == 'Windows':
    redis_host = 'zjqd.cfirs.com'  # Redis服务器的主机名或IP地址
elif platform.system() == 'Linux':
    redis_host = '************'

redis_port = 5198  # Redis服务器的端口号
guang_r0 = Redis(host=redis_host, port=redis_port, db=0, password='ls3956573')# 存储1688 ID对应的本地图片链接
guang_r1 = Redis(host=redis_host, port=redis_port, db=1, password='ls3956573')# 用于存储在线的店铺listing数据
guang_r2 = Redis(host=redis_host, port=redis_port, db=2, password='ls3956573')# 用于存储汇率
guang_r3 = Redis(host=redis_host, port=redis_port, db=3, password='ls3956573')  # 用于储存浏览过的listing数据
guang_r4 = Redis(host=redis_host, port=redis_port, db=4, password='ls3956573')  # 用于储存ozon广告记录
guang_r5 = Redis(host=redis_host, port=redis_port, db=5, password='ls3956573')  # 用于储存WBcookies
guang_r6 = Redis(host=redis_host, port=redis_port, db=6, password='ls3956573')  # 用于刷单的记录
guang_r7 = Redis(host=redis_host, port=redis_port, db=7, password='ls3956573')  # 用于刷单买家号的记录

