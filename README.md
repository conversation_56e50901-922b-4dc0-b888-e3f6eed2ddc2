# Excel数据拆分处理程序

## 概述

这是一个可重复使用的Python程序，用于处理Excel文件数据拆分任务。程序能够从主数据文件中按区域拆分SKU库存数据，并生成符合指定格式的区域文件。

## 功能特点

- ✅ **自动数据分析**: 分析主数据文件和参考格式文件的结构
- ✅ **智能区域识别**: 自动识别20个区域的库存数据
- ✅ **格式严格匹配**: 输出文件严格按照参考文件格式生成
- ✅ **数据验证**: 自动验证输出文件的格式和数据完整性
- ✅ **错误处理**: 完善的错误处理和日志记录
- ✅ **处理报告**: 生成详细的处理报告
- ✅ **模块化设计**: 可重复使用的模块化程序结构

## 文件结构

```
E:/python_project/
├── excel_data_splitter.py          # 主程序文件
├── analyze_excel_structure.py      # 文件结构分析工具
├── README.md                       # 使用说明
├── file/                           # 输入文件目录
│   ├── 20250825库存情况和分配计划-对外.xlsx    # 主数据文件
│   └── Дальний Восток-远东-20250825.xlsx     # 参考格式文件
├── output/                         # 输出文件目录
│   ├── Moscow-莫斯科、莫斯科州和偏远地区-20250825.xlsx
│   ├── Saint-Petersburg-圣彼得堡和西北分部-20250825.xlsx
│   ├── Far-East-远东-20250825.xlsx
│   ├── ... (其他区域文件)
│   └── processing_report_*.txt     # 处理报告
└── excel_splitter.log             # 日志文件
```

## 依赖要求

```bash
pip install pandas openpyxl
```

## 使用方法

### 1. 基本使用

```bash
python excel_data_splitter.py
```

### 2. 自定义配置

修改 `excel_data_splitter.py` 中的配置：

```python
# 配置文件路径
main_file = "file/20250825库存情况和分配计划-对外.xlsx"
reference_file = "file/Дальний Восток-远东-20250825.xlsx"
output_dir = "output"

# 日期字符串 (格式: YYYYMMDD)
date_str = "20250825"
```

### 3. 程序化使用

```python
from excel_data_splitter import ExcelDataSplitter

# 创建数据拆分器
splitter = ExcelDataSplitter(
    main_file_path="file/主数据文件.xlsx",
    reference_file_path="file/参考格式文件.xlsx",
    output_dir="output"
)

# 运行处理流程
success = splitter.run(date_str="20250825")

if success:
    print("处理成功!")
else:
    print("处理失败，请查看日志")
```

## 输入文件要求

### 主数据文件格式
- **文件类型**: Excel (.xlsx)
- **必需列**: `产品型号` (SKU列)
- **区域列**: 包含各区域的库存数量数据
- **数据行**: 从第3行开始为实际数据（前两行为标题）

### 参考格式文件格式
- **文件类型**: Excel (.xlsx)
- **列结构**:
  - `артикул`: SKU编号
  - `имя (необязательно)`: 名称(可选)
  - `количество`: 数量

## 输出说明

### 输出文件
- **命名规则**: `{英文区域名}-{中文区域名}-{日期}.xlsx`
- **格式**: 严格按照参考文件格式
- **内容**: 仅包含该区域有库存的SKU数据

### 支持的区域
程序支持以下20个区域的数据拆分：

1. 莫斯科、莫斯科州和偏远地区 (Moscow)
2. 圣彼得堡和西北分部 (Saint-Petersburg)
3. 南部 (South)
4. 乌拉尔 (Ural)
5. 喀山 (Kazan)
6. 雅罗斯拉夫尔 (Yaroslavl)
7. 萨拉托夫 (Saratov)
8. 西伯利亚 (Siberia)
9. 乌法 (Ufa)
10. 哈萨克斯坦 (Kazakhstan)
11. 沃罗涅日 (Voronezh)
12. 高加索 (Caucasus)
13. 秋明 (Tyumen)
14. 远东 (Far-East)
15. 萨马拉 (Samara)
16. 亚美尼亚 (Armenia)
17. 加里宁格勒 (Kaliningrad)
18. 白俄罗斯 (Belarus)
19. 克拉斯诺亚尔斯克 (Krasnoyarsk)
20. 吉尔吉斯斯坦 (Kyrgyzstan)

### 处理报告
程序会生成详细的处理报告，包含：
- 处理统计信息
- 输出文件列表
- 错误信息（如有）
- 处理时间戳

## 日志和调试

- **日志文件**: `excel_splitter.log`
- **日志级别**: INFO
- **编码**: UTF-8
- **内容**: 详细的处理过程和错误信息

## 错误处理

程序包含完善的错误处理机制：

1. **文件验证**: 检查输入文件是否存在
2. **数据验证**: 验证数据格式和完整性
3. **输出验证**: 验证生成文件的格式正确性
4. **异常捕获**: 捕获并记录所有异常
5. **回滚机制**: 处理失败时的清理操作

## 性能优化

- 使用pandas进行高效数据处理
- 批量处理减少I/O操作
- 内存优化避免大数据集问题
- 并行处理支持（可扩展）

## 版本信息

- **版本**: 1.0
- **作者**: AI Assistant
- **日期**: 2025-08-22
- **Python版本**: 3.7+

## 许可证

本程序仅供学习和内部使用。

---

## 常见问题

### Q: 如何处理不同日期的文件？
A: 修改程序中的 `date_str` 参数，或者从文件名中自动提取日期。

### Q: 如何添加新的区域？
A: 在 `region_mapping` 和 `region_english_names` 字典中添加新的区域映射。

### Q: 输出文件格式不正确怎么办？
A: 程序包含自动验证功能，会在日志中显示具体错误信息。

### Q: 如何批量处理多个文件？
A: 可以编写循环脚本调用 `ExcelDataSplitter` 类处理多个文件。