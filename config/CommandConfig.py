#!/usr/bin/python
# -*- coding: UTF-8 -*-
from common.CommandClient import CommandClient





class CommandConfig:
    # 应用id
    # app_id = '202211101040096593799409664'
    # 应用私钥lens
    # private_key = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDPMcFv7/UrJLMrcV1LI2QBVkEWVIPMNjJl91H100W6zl2Qk5KeYJBpJCyoZ0h/zaSLT6usivTKlWCngYGPpsgt7UgNkzYQX9OnV5oRjcblHTG+pW60AfC5HnpXP2ZmOWgMJubt1cN+I5iVpTK3j4GDMmDkMq14ijkroKMqIUYdMrKCLmNzy4RMybU5wFIZVV5oDV8oeZpBfwZeJqnAGYvfnB6qSdp7Du7lJ/PXAoELcPaqTCp3DTVcD4w1rdpVIEYmqy58zLLh1gBW07/yoCjBx+M+FlQCiQNCe+v49Y+9iXTcmyTfG+QrS743+U9CFa2KKOOUPi4xmcgT+iJg1ZUvAgMBAAECggEBAIUFAZZ27OTc8/aRGn8x4nOFR9YpPlBt/x89kC4NjLfo42L9i2UIw83svosDfCUpBlm2NZZZw90H1/zShTaPsfbiXp7wgwYyL3VBJZXdevL/Mj+egBsrH7wJmCvJdVltw6IBm8LXuWGTQtPYMl5s0I5FWrVG394Ch7+9quNSZ2w4RpZSmK4QNIHrihW1atQakx3aRDoPobISIqmZC0NHLtFACW8ebaji1/ADBPA0aEHyNcLGrK4AvqRwzBsOpfwiy+QFrD2M8LNxBC2TMxtuVpl0rtWiYRa9fqpr1x+wlqq2GEB8lgItyvpM2gVxXxrfD+5CYw+ngmyUHULbKBouUAECgYEA+HC/7gdvLqLw8tjpWQ/eST71lvjx1/j3LKUXfPY+seMjiGdt+ur/NXB8M2/So+zrd4Rgy81ROuTWup90t2gs+s0jkQMFILzF5SflAW6pFAwFxxlOd51Uc5s4XQh0D5ttUROZ2FvvHocVN9wh+/gyLDRvgH1JTYpuJBvp84zrSAECgYEA1X+3OMCCHXepmvlwWCtHl8C+WnbT7kA1UUz6byYXXbjxBFIIvaWDCVtLEjlvvBQ0oxtasujCfaZWL0TPR75SMTAiY0iMwHA+Pw2eTG395SyNzQIlq+Vkt2vde80HlfTAm1CzItFPxkSAMUXpH/i3Tz2BpWvXngjgSuf3rnB7XS8CgYAVpkWjaXRv3PUgRHS5NR/KxDjEijcQ+foYIt7UHeVCvSdoiJLV7cqFjqLUHKd/b3LpfFivCdpNRonLw395SBO7W0sBJQE6ZBjeqkZThKApRg7u1a63z7b/SAR2Xvk2LvYsvYeHiiNhRqY7suwnN+6vWXZp/QD5R9nnPaN6fMo4AQKBgGLkvuUN3vdxV2HOyyg8G/SCNmpHApIoscnQA2z60vX51cJvkVbvVMw/un1dl5EiNuOapx+aS4TSSLeqgu0zjnT0W+lrUK9PDrY8Q6gZhvLh+WkN5o9COa3wy+4toabncVRAREoLdI1da5fnTz4dAiQ1VFlgo8azS9nGq2bk+W/HAoGBAJVwTjeRk6G91TvGvPi7Er+y575IdLuoMR6gkmiIOtwYNTSTMWPG6JsdggAy8EHk9RRHh5dT+rdPdh/8hioqbQCFSYvuIjQHH+EyJOQe8VgwXbNwVQ5Ew5AlR1UK0SsFYxwApO8zDFcasot8bspu+YT4zXCAveiD29BWF5bwPR7P"

    # 应用id
    app_id = '202409101282925298807074816'
    #应用私钥-撸毛
    #private_key = 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCO5JVTcpimf4cFXfevw/ljCkwGHwiEfdw/D6v7CUJYgJO3+P5ndApuSfvjfeEE6Tz3YTcfIUnHkFC8xA2ko2dIqUaErjY1VgJkC93wBnZ8Cpw5oqaXHnA68RtODwpfdHQvzxOdexTAyB5t/YeXY+EBDWtkBufuw72K++8b1LF48HCyfXVrQpI/lQrhTVzvEUo9pk4Y5vdwwl1UHgkB/X3vDxuc8JZln8ubw+gJw1KkT1yf/akq4Bka69F2FvEhIzW2ycvzIIecfB90/Ov4nzW3uIlLC98eHTetzEKwkLmDejpLTVr0JWakFcVGZ4h9b9r37vtwTk8zfe6EY64R7vdFAgMBAAECggEADeUylDPPnllGYwI5RNFU3gmxTOMVq/SlR0iZ28aRuhN+yeQfsSW6ofD12rByAk1ADXpZpuBQ0WQ6RbD29qyhLZKwddSPFu6vOtVglqe6O+O4P26OrqFaHpE1q4iQ5KlpZwDHEohWg0yykNWXquQejECm0cpOIYzNUMeg/97WB0GM+CmYvGjnOWnssmokyDiUSeJMw474xiWj4bFK+AaCY3FK2+PsAGd/qem5xB3KUVyHuprVHvL6b8NEGlKA0Az5KyfIrhCX6+AuRtN9KAxfLwKMOwWYdG3JJFQKKdxO42TL4PFGHfgO/LXjGE5Q3IP+C4yijJZ+abvEg1B426+nvQKBgQDVZNLfp0R7jAUNTrTgB/otpkAjmLY0rHpCyN1RVLgIuD3JJ26cnxbclMyQoJ1k1lBDw1L9PU6YU/S6RK4kic4bizTCEefv6hoHnexWBHuTkGeHUoOXITuV2xeBrHWb21VnEE2gOzcA3dUKqiuEybP2s5rjVJ4QTRixtdO72/9TnwKBgQCrbECzz+WuKvCsiYYVFxQJ0EurFsGZOgq5PXpLPyXJpvvPLYhoOnvqgv5slh2YrfKFO9iEFrKq4p0dzB4orW8p1Mci85uSGCsimvDFsY6is1ukmBeqw3g0W1CsDTSX54s/DkjBTOiTIpE8grXXe3ZgP4+pWhwuV034MbN9OxjqmwKBgQC2kzT4thwZuIr5/8UxiKG8POnTiu0Z1Bgm28S/uURb8YarfFlfF55vy7/aXhFMHTE/SPbQRIviOLLCS9DS4jS9zDSvdrKCykDLE4sJbXxtnLd3SFneDEj2xiUuhWd6mgZPz9sapI9C5olIPqBNVGXtRW3X4qX6PHxMT82AhZHG8QKBgHN6oHfBZJOY37sLheTtQBWAWYku8kp7vD+ft6O7LYST1exyWwMD6vhHOIgD9HonLEouz6Pj+oZ91KP/Ho3+j/VZHrFf9HxNhblxMSOsgk7+jozp3H+v4Gk83rKYzsZKOBbJU5/MZJzB258s2r0Hse4Sw3+tcXCSZbb+HFEPn7BjAoGAFmlB5m2TbzOCL1BnRvtXEf4JaDwh/VrqdSEAH9No33rYQhSSF1jCR4xDvmDCNeXLjriUCb+9d089IIl7OLB82ijR/bAUy8NsRNRL0/ckjaZTiCPb5X5DRZ07NwDsNXXHe394FirBPJe7WQs0B2wRcZhR1+LA3nQsAJwGEBVGcUs='
    private_key = 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCyAEVEQkpA4ns320iShmhkQutrzWRjJLxL20R+AVHJr2Qpq/kkw+Oms2OVJQX1k4tYLegb7zU2QIMEIWCONDEiUcirnlUaGqDIiUTx0ElOK7EOdPLhU9P6fHaTUPfP5dVEeVypXF4DHIw2hCjFg//6dTlCFzvfFYXx6lcd37K0bDZsxq9MOWPxGCFBxjOqHKvyOBAbFyXli9BDGSlOEqsID7eZ7lgHqen+Jl7Xj83oizG6L8CQFF5E531ggYwp2oXKgTbFPKxZh9rw7TuQyaPeJpJx+Spwa31JU9ARJ4oNT384lDHgOE0iJZacMK26nRjsis3o4Mutj9LxZiQ9c4S9AgMBAAECggEAWswUiSsfYVWl8OokbMmu+mXAXO8aZBBbFH62w2e85ppXB4K3xe8+csxS4cNO9OPcUpCoM4ejpxblPfggnqk03wLEz/YmBvjKR0DuMENFAWD8Ihm5wwLVwJI4khDdN+lPDmyxvZpmQkQPqcdkXMxmiMW+/V2IptT9zFbzNUPkjm9ALxx+efOOgi79Y+LTrgEVb3Gu8PuAHc/FV9GqX7y2FpiTIKWl7OOP+rqNaDD4nm8nC2A9fiGdKD3WvoAaElMhvjU/xglh+R5Ca1ClyLgbn4M3tBL4SADRUGQGJ1osmMSow6mORIACEHqgph8G29wU22GUMw7rc8Km7Xz/uKh/QQKBgQD5bjrXmEp2/9OKYXOK0eC6I6fsIIhJrpt/v4pbjN6Ol9ffgNiWtWPqdImvJbJNSHhUl28Yl9IvR5VGVAtaYYxgBsynj9xyos4sJPFF4ip/EHzia4Socp4vzPdhqbCxlvAFzxSlyTGadiE9mR30afN+gPRwBzh9cDr2upOV54x3eQKBgQC2sG58W3hhIONO2w17fSWC8to4FT/uOyLU74J5JALrNDy7XzWH1n8uEGaDmHQrxCTFj58IIis3MyXWlqm+Cg+oz0a7Qvees25B0HqUneXaayGZbMIQLIGOmTCnuEg1wfnzaeDwh3SswaMleCXgTUFYMyVm9W/FmqIbVB8rrNjyZQKBgQCEZsLNVoWQjVPZDMmsUqQbqnmkmdA+bHUWZrImzqwEvQ1WEqrltPuIpLvBQR1YcLOPE6NhRpXHixPt9Qbhvw4omvNfrrmyEEGopSN9mQQ91p60Aaa1U8IsVXyrVsv4OMrICphHOHkgKLyUWgbQir051XHrVanizARAm+FjnTmu4QKBgFAajFd6xA3Sndi1s/OxrAD++zAcISwUQfGcE1mKsLzw6rA9zvsA0kMgbdLFxipqlrUJhHHMiaZqhuzQoxzwzGz4dcsmUe0EQBQblaQUl/RthQ4A4ha1bRlZkjv4UbFAnJhelILi4gXd4etMA0+WkFBcHMpIU/oRE4NoH0LHR1kBAoGBAOQf197X6E3bZdNpDsy3DGzjqj0WcfTI2joOfqWwub4px/UxHk5JpM57CMXTbnGh/QW/5zmgBlu5OEOZJaJdKXyOFavwZ5R5aX5eYsUvM9vEaplv3aYmMGmcqulPZkLHcSt+dmCKMuYUDH13bk/fwkP6siSklc+b85XU8AXr+J5e'

    # hubstudio连接器程序路径
    connector_path = r"C:\Program Files\Hubstudio\hubstudio_connector.exe"
    # hubstudio程序路径，不填写则默认从连接器路径下查找
    hubstudio_path = r"C:\Program Files\Hubstudio\Hubstudio.exe"
    # 连接端口
    socket_port = 6873
    # webdriver程序路径
    webdriver_path_100 = r".\config\chromedriver100.exe"
    webdriver_path_105 = r".\config\chromedriver105.exe"


def create_client():
    """
    可以以导入模块的方式获得一个单例对象
    Example:
    from config.CommandConfig import client
    response = client.execute(request)
    """
    # 应用id
    app_id = CommandConfig.app_id
    # 应用私钥
    private_key = CommandConfig.private_key
    # 连接端口
    socket_port = CommandConfig.socket_port
    # 创建请求客户端
    return CommandClient(app_id, private_key, socket_port)


client = create_client()
