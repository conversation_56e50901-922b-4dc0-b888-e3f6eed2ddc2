import json
import os

import random
import re

import time as p_time
from datetime import datetime, time, timedelta

import redis
from playwright.sync_api import Page

from browser.start_browser import open_browser
from change_ip import ChangeIP
from common.ExchangeRate import ExchangeRate
from db.client_mongdb import login_mongdb_review
from dingding_robt import DingTalkRobot, MessageBuilder
from info import BuyerInfo
from lendian.api import LDApi
from lendian import logger
from server import ProxyServer

m_db = login_mongdb_review()
r_db = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
ali_db = redis.Redis(host='************', port=6379, db=5, decode_responses=True, password="AirSmile@1688")
to_day_shopping = {
"79801815917_wb":1,
"79104981269_wb":1,
"79153379476_wb":1,
"79104980323_wb":1,
"79153391201_wb":1,
"79801812446_wb":1,
"79104981386_wb":1,
"79801811276_wb":1,
                   }
ld_sdk = LDApi(r'D:\leidian\LDPlayer9')  # 链接雷电模拟器驱动
ip = ChangeIP(r_db, ld_sdk.adb)
ex_rate = ExchangeRate(r_db)
robot = DingTalkRobot()


def save_cookies(page: Page, buyer):
    file_path = './cookies/' + buyer + '.json'
    with open(file_path, "w") as f:
        f.write(json.dumps(page.context.cookies()))
    f.close()


def load_cookies(page: Page, buyer):
    file_path = './cookies/' + buyer + '.json'
    if os.path.exists(file_path):
        with open(file_path, "r") as f:
            cookies = json.loads(f.read())
        f.close()
        page.context.add_cookies(cookies)
        if is_login(page, buyer):
            page.pause()
    else:
        page.pause()


def is_login(page: Page, buyer) -> bool:
    page.goto('https://www.ozon.ru', wait_until="networkidle", timeout=10000000)
    page.wait_for_timeout(3000)
    if page.get_by_text('Войти').is_visible():
        return True
    return False


def mark_settle_order(data):
    # 判断是否已经存在任务号的结算记录
    settle_record = m_db['settlement'].find_one({'taskId': data['taskId']}, {'_id': 0})
    if settle_record:
        logger.info('结算记录已存在, taskId: {}'.format(data['taskId']))
        return None

    # 在数据库中查找data['taskId']这个任务号对应的订单信息
    order = m_db['orders'].find_one({'taskId': data['taskId']}, {'_id': 0})
    # 根据订单信息中的用户信息,在数据库中查找data['taskId']这个任务号对应的订单信息,用于获取计算结算价格
    userInfo = m_db['userinfo'].find_one({'userId': order['userId']}, {'_id': 0})

    rate = ex_rate.get_rate()
    if rate == 0 or rate is None:
        logger.error('汇率获取失败, 请检查网络连接')
        return '汇率获取失败, 请检查网络连接'
    logger.info('汇率为: %s', rate)
    date_str = datetime.now().strftime("%Y-%m-%d")
    if order['orderPcs'] > 0:
        esCost = round(data['price'] * rate / 100, 2)
        orderPcs = userInfo['info'].get('PURCHASE_ORDER_BASE_PRICE')
        like = 0 if order['like'] == 0 else userInfo['info'].get('PURCHASE_ORDER_LIKE_PRICE')
        cart = 0 if order['cart'] == 0 else userInfo['info'].get('PURCHASE_ORDER_ADD_ON_PRICE')
        review = 0 if order['review'] == 0 else userInfo['info'].get('PURCHASE_ORDER_REVIEW_PRICE')
        espay = (like + cart + review + orderPcs) * userInfo['info'].get('FEE_DISCOUNT')
    else:
        esCost = 0
        orderPcs = 0
        like = 0 if order['like'] == 0 else userInfo['info'].get('UNPURCHASED_ORDER_ONLY_LIKE_PRICE')
        cart = 0 if order['cart'] == 0 else userInfo['info'].get('UNPURCHASED_ORDER_ONLY_ADD_ON_PRICE')
        review = 0
        espay = (like + cart + review + orderPcs) * userInfo['info'].get('FEE_DISCOUNT')

    data['esPay'] = round(espay, 2)
    data['esCost'] = round(esCost, 2)

    data2 = {
        'updateTime': datetime.now(),
        'status': 3,
        'price': round(data['price'] / order['orderPcs'], 2),  # 修正真是付款单价
        'esCost': round(esCost, 2),
        'orderId': data.get('orderId'),
        'buyer': data['buyer'],
    }
    logger.info(f'更新任务号{data['taskId']} {data2}')

    # 更新订单状态
    m_db['orders'].update_one({'taskId': data['taskId']}, {"$set": data2})

    # 添加结算记录,在数据库settlement中
    ##先根据字段createDate的日期值,查询最新的结算信息
    settle_date_latest = m_db['settlement'].find({'userId': order['userId']}, {'_id': 0}).sort(
        [('createDate', -1)]).limit(1)
    settle_date_latest = settle_date_latest[0]
    ##余额为settle_date_latest['balance']
    balance = settle_date_latest['balance']
    ##生成结算号,结算号后四位为当前时间戳,时间戳精确到毫秒,取后8位
    t = int(p_time.time() * 1000)
    if esCost > 0:
        balance = round(balance - esCost, 2)
        settle_data_goods = {
            'taskId': data['taskId'],
            'settlementId': 'O' + order['userId'] + str(t + 1)[-8:],
            'createDate': datetime.now(),
            'itemType': '货款',
            'amount': round(-esCost, 2),
            'balance': balance,
            'userId': order['userId'],
        }
        logger.info(f'添加货款结算记录:{data['taskId']}   {settle_data_goods}')
        # 添加结算记录
        result_goods = m_db['settlement'].insert_one(settle_data_goods)

    if espay > 0:
        balance = round(balance - espay, 2)
        settle_data_commission = {
            'taskId': data['taskId'],
            'settlementId': 'O' + order['userId'] + str(t + 1)[-8:],
            'createDate': datetime.now(),
            'itemType': '佣金',
            'amount': round(-espay, 2),
            'balance': balance,
            'userId': order['userId'],
        }
        logger.info(f'添加佣金结算记录:{data['taskId']}   {settle_data_commission}')
        result_commission = m_db['settlement'].insert_one(settle_data_commission)
    result_data = [data2, settle_data_goods, settle_data_commission]

    return str(result_data)


def should_handle_response(response,page_num):
    if re.compile(r'search\.wb\.ru/exactmatch/ru').search(response.url) and page_num < 7:
        print(response.url)
        return True
    if re.compile(r'card\.wb\.ru/cards').search(response.url) and page_num >= 7:
        print(response.url)
        return True
    return False
def buy_wb_item(item_list, page):
    if len(item_list) == 0:
        return None
    page.goto('https://www.wildberries.ru', wait_until="networkidle", timeout=1000000)
    new_item_list = []
    r_cart_price = 0
    budget_amount = 0
    buyer = ""
    page.pause()
    for item in item_list:
        buyer = item['buyer']
        flag = True
        page_num = 1
        # keyword_txt = item['keyword']
        keyword_txt =''
        product_id = ''
        budget_amount += item['price']
        if keyword_txt != '':
            page.get_by_role("searchbox", name="Найти на Wildberries").type(keyword_txt, delay=217)
            page.wait_for_timeout(1000)
            while flag:
                if page_num >= 7:
                    product_id = str(item['productId'])
                    flag = False
                with page.expect_response(lambda response: re.compile(r'search\.wb\.ru/exactmatch/ru').search(response.url)) as resp_info:
                    if page_num == 1:
                        page.get_by_label("Поиск товара").click()  # 查找商品
                        page_num += 1
                    else:
                        logger.info(f'商品:{item["productId"]} 没有找到-翻页{page_num}')
                        if page.get_by_role("link", name=f"{page_num}", exact=True).count != 0:
                            if page.get_by_role("link", name=f"{page_num}", exact=True).is_visible():
                                page.get_by_role("link", name=f"{page_num}", exact=True).click()  # 翻页
                                page_num += 1
                    page.wait_for_timeout(8000)
                    page.wait_for_load_state("networkidle")
                search_temp = resp_info.value.json().get('data', None)
                if search_temp is not None:
                    if search_temp.get('total', 0) == 0:
                        logger.info(f'商品:{item["productId"]} 没有找到')
                    elif search_temp.get('total', 0) == 1:
                        page.locator(f"#c{item['productId']}").get_by_label("Добавить в избранное").click()
                        page.get_by_role("button", name="Добавить в корзину").click()  # 加入购物车
                        page.wait_for_load_state("networkidle")
                        page.wait_for_timeout(2000)
                        new_item_list.append(item)
                        flag = False
                    elif search_temp['total'] > 1:
                        search_items = search_temp.get('products', [])
                        for index, search_item in enumerate(search_items):
                            if index in [0, 9, 18, 27, 36, 45, 54, 63, 72, 81]:
                                logger.info(f'商品:{item["productId"]} 没有找到-滚动')
                                page.wait_for_timeout(1320)
                                page.evaluate("window.scrollBy(0, window.innerHeight)")
                            if search_item['id'] == int(item["productId"]):
                                page.locator(f"#c{search_item['id']}").scroll_into_view_if_needed()  # 点击商品
                                #加购
                                page.locator(f"#c{search_item['id']} a").first.click()
                                #喜欢
                                # page.locator(f"#c{search_item['id']} a").last.click()
                                page.wait_for_timeout(1020)
                                new_item_list.append(item)
                                flag = False
                                break
        elif keyword_txt == '':
            page_num = 8
            product_id = str(item['productId'])
        if page_num > 7 and product_id != '':
            with page.expect_response(lambda response: re.compile(r'card\.wb\.ru/cards').search(response.url)) as resp_info:
                if keyword_txt != "":
                    page.get_by_label("Очистить поиск").click()  # 清空搜索框
                page.get_by_role("searchbox", name="Найти на Wildberries").type(product_id, delay=217)
                page.get_by_label("Поиск товара").click()  # 查找商品
            page.wait_for_timeout(8000)
            page.wait_for_load_state("networkidle")
            search_temp = resp_info.value.json().get('data', None)
            if search_temp is not None:
                search_items = search_temp.get('products', [])
                if len(search_items) == 1:
                    if search_items[0].get('totalQuantity',0) <= 0:
                        logger.info(f'商品:{item["productId"]} 库存不足')
                        continue
                    cart_price = search_items[0]['sizes'][0]['price']['product']*0.01
                    r_cart_price += cart_price
                    if cart_price > item['price']:
                        item['price'] = cart_price
                    #page.locator(f"#c{search_items[0]['id']}").get_by_label("Добавить в избранное").click()  # 加入收藏夹
                    page.get_by_role("button", name="Добавить в корзину").click()  # 加入购物车
                    page.wait_for_load_state("networkidle")
                    page.wait_for_timeout(2000)
                    logger.info(f'{buyer} 商品:{product_id} 加入购物车 单价:{cart_price}₽')
                    new_item_list.append(item)
                else:
                    logger.info(f'商品:{item["productId"]} 没有找到')

    if len(new_item_list) == 0:
        return None
    page.get_by_role("link", name="Корзина").click()  #购物车
    # 发送纯文本消息
    robot.send(
        message_type="text",
        content=MessageBuilder.text(f'{buyer} 购物车中共有{len(new_item_list)}件商品, 预估金额:{budget_amount}₽'),
        at_all=False
    )
    page.pause()
    # 等待选择页面支付方式
    with page.expect_request(lambda request: re.compile(r'wbxoofex\.wildberries\.ru/api/v9/order\?sticker=').search(request.url),timeout=3000_000) as req_info:
        page.get_by_role("button", name="Заказать").click()  #结算
        page.wait_for_timeout(random.randint(5, 15)*1000)
    json_order = req_info.value.post_data_json.get('order', {})

    page.get_by_role("link", name="Перейти в доставки").click()  # 支付完成
    order_id = json_order.get("order_uid", "")
    order_amount = json_order.get("payment", {}).get("amount", 0) * 0.01
    payment_type_name = json_order.get("payment", {}).get("paymentType", {}).get("selectedBankCard", {}).get('name', "")
    bank_card_id = json_order.get("payment", {}).get("paymentType",{}).get("bankCardId", "")
    logger.info(f'{buyer} 订单号:{order_id} 支付方式:{payment_type_name} 银行卡:{bank_card_id}')
    logger.info(f'{buyer} 订单号:{order_id} 下单数:{len(new_item_list)}')
    logger.info(f'预估金额:{budget_amount}₽ ,订单金额:{r_cart_price}₽ ,付款金额:{order_amount}₽')
    to_day_shopping["%s_wb".format(buyer)] = 1
    for item in new_item_list:
        price_o = item['price']
        price_f = int(item['price'] + item['price'] * 0.025)
        logger.info(f'任务:{item['taskId']}, 原价格:{price_o}₽, 修正价格:{price_f}₽')
        item['price'] = price_f
        updata_data = {'$set': {'status': 3, 'orderId': order_id, 'buyer': buyer, 'price': item['price'], 'updateTime': datetime.now()}}
        m_db['orders'].update_one({'taskId': item['taskId']}, updata_data)
    return


def buy_yandex_item(item_list, page):
    if len(item_list) == 0:
        return None
    page.goto('https://market.yandex.ru/', wait_until="networkidle", timeout=1000000)
    page.locator('#header-search').type(item_list[0]['keyword'], delay=217)
    page.get_by_role("button", name="Найти").click()
    page.pause()
    to_day_shopping["%s_yandex".format("")] = 1


# 定义一个函数来滚动页面并查找元素

def buy_ozon_item(ozon_items, page, buyer_info):
    load_cookies(page, buyer_info.phone)
    if len(ozon_items) == 0:
        return None
    page.goto('https://www.ozon.ru', wait_until="networkidle", timeout=1000000)
    save_cookies(page, buyer_info.phone)
    ozon_page_num = 1
    data_jsons = []
    data_page_list = []

    def handle_response(response):
        if response.status == 200:
            url = response.request.url
            if ('entrypoint-api.bx/page/json/v2?url=%2Fcategory%2F' in url) or (
                    'entrypoint-api.bx/page/json/v2?url=%2Fsearch%2F' in url):
                data_json = response.json()
                if data_json is None:
                    return
                ws_json = data_json.get('widgetStates', [])
                for key in ws_json:
                    sr_dict = ws_json.get(key, None)
                    if sr_dict is None:
                        continue
                    sr_json = json.loads(sr_dict)
                    if sr_json.get('page') is not None and sr_json.get('page') != '':
                        print(sr_json.get('page'))
                        data_jsons.append(sr_json)
                        data_page_list.append(sr_json)

    new_ozon_items = []
    r_cart_price = 0
    budget_amount = 0
    for i in range(len(ozon_items)):
        budget_amount += ozon_items[i]['price']
        keyword_txt = ozon_items[i]['keyword']
        product_id = str(ozon_items[i]['productId'])
        if keyword_txt != '':
            page.get_by_placeholder("Искать на Ozon").type(keyword_txt, delay=267)
            page.wait_for_load_state("networkidle")
            flag = True
            page.on('response', handle_response)
            while flag:
                if len(data_jsons) >= 30:
                    keyword_txt = ''
                    flag = False
                    page.wait_for_timeout(1000)
                    page.get_by_placeholder("Искать на Ozon").locator('..').locator('div > svg').click()
                    page.wait_for_timeout(1000)
                    page.locator("form path").nth(1).click()
                # 尝试查找元素
                for data in data_page_list:
                    items = data.get('items')
                    for item in items:
                        if item.get('skuId') == product_id:
                            new_ozon_items.append(ozon_items[i])
                            current_cart_num = item.get('multiButton').get('ozonButton').get(
                                'addToCartButtonWithQuantity').get('currentItems', 0)
                            if current_cart_num > 0:
                                print('商品已存在购物车')
                            else:
                                link = item.get('action').get('link')
                                btn_text = item.get('multiButton').get('ozonButton').get(
                                    'addToCartButtonWithQuantity').get('text')
                                page.locator(f'a[href="{link}"]').first.locator('..').get_by_role("button",
                                                                                                  name=f'{btn_text}').click()
                                print('找到商品')
                            # 清空
                            flag = False
                            page.wait_for_timeout(1000)
                            page.get_by_placeholder("Искать на Ozon").locator('..').locator('div > svg').click()
                            page.wait_for_timeout(1000)
                            page.locator("form path").nth(1).click()

                if ozon_page_num * len(data_jsons) == 0:
                    page.get_by_label("Поиск").click()
                    page.wait_for_load_state("domcontentloaded")
                    if len(page.locator('div[id^="state-searchResultsV2"]').all()) == 1:
                        data_json = json.loads(
                            page.locator('div[id^="state-searchResultsV2"]').all()[0].get_attribute('data-state'))
                        print(data_json.get('page'))
                        data_jsons.append(data_json)
                        data_page_list.append(data_json)
                    page.wait_for_timeout(1_000)
                if page.get_by_role("link", name=f"Дальше").count() == 1:
                    for data in data_page_list:
                        items = data.get('items')
                        for item in items:
                            if item.get('skuId') == product_id:
                                new_ozon_items.append(ozon_items[i])
                                flag = False
                                print('找到商品')
                                page.pause()
                    print('点击翻页')
                    ozon_page_num += 1
                    page.get_by_role('link', name=f'{ozon_page_num}', exact=True).scroll_into_view_if_needed()
                    page.wait_for_timeout(3_000)
                    data_page_list = []
                    page.get_by_role('link', name=f'{ozon_page_num}', exact=True).click()
                    page.wait_for_timeout(1_000)
                    if len(page.locator('div[id^="state-searchResultsV2"]').all()) == 1:
                        data_json = json.loads(
                            page.locator('div[id^="state-searchResultsV2"]').all()[0].get_attribute('data-state'))
                        print(data_json.get('page'))
                        data_jsons.append(data_json)
                        data_page_list.append(data_json)
                else:
                    page.wait_for_load_state("networkidle")
                    page.evaluate("window.scrollBy(0, window.innerHeight/2)")
        if keyword_txt == '':
            page.get_by_placeholder("Искать на Ozon").type(product_id, delay=267)
            #page.wait_for_load_state("networkidle")
            page.get_by_label("Поиск").click()
            page.wait_for_load_state("networkidle")
            # page.pause()
            if page.get_by_role("button", name="Добавить в корзину").is_visible():
                page.wait_for_timeout(7_00)
                cart_state = page.locator('div[id^="state-webAddToCart"]').all()[0].get_attribute('data-state')
                cart_item = json.loads(cart_state)
                if cart_item.get('sku',"") == product_id:
                    page.get_by_role("button", name="Добавить в корзину").click()
                    page.wait_for_timeout(7_00)
                    cart_price_str = cart_item.get('price', 0).replace(' ', '').replace(' ', '')
                    cart_price = int(cart_price_str.replace('\xa0', '').replace('₽', ''))
                    if ozon_items[i]['price'] < cart_price:
                        ozon_items[i]['price'] = cart_price
                    r_cart_price += cart_price
                    logger.info(f'{buyer_info.phone} 商品:{product_id} 加入购物车 单价:{cart_price}₽')
                    new_ozon_items.append(ozon_items[i])
                    page.wait_for_timeout(7_00)
            elif len(page.locator('div[id^="state-webAddToCart"]').all()) > 1:
                cart_state = page.locator('div[id^="state-webAddToCart"]').all()[0].get_attribute('data-state')
                cart_item = json.loads(cart_state)
                cart_sku = cart_item.get('sku',"")
                cart_price_str = cart_item.get('price', 0).replace(' ', '').replace(' ', '')
                cart_price = int(cart_price_str.replace('\xa0', '').replace('₽', ''))
                if ozon_items[i]['price'] < cart_price:
                    ozon_items[i]['price'] = cart_price
                r_cart_price += cart_price
                cart_qyt = cart_item.get('quantity', 0)
                logger.info(f'{buyer_info.phone} 商品:{product_id} 已在购物车 单价:{cart_price}₽')
                if cart_sku == product_id and cart_qyt > 0:
                    new_ozon_items.append(ozon_items[i])
                    continue
            elif len(page.locator('div[id^="state-searchResultsV2"]').all()) == 0:
                logger.warning(f'{buyer_info.phone} 商品:{product_id} 被下架')
                page.locator("form path").nth(1).click()
                page.wait_for_timeout(7_00)
                continue
            elif len(page.locator('div[id^="state-searchResultsV2"]').all()) == 1:
                search_item = \
                    json.loads(
                        page.locator('div[id^="state-searchResultsV2"]').all()[0].get_attribute('data-state')).get(
                        'items')[0]
                if search_item.get('skuId') == product_id:
                    button_quantity = search_item.get('multiButton').get('ozonButton').get(
                        'addToCartButtonWithQuantity')
                    if button_quantity.get('maxItems') == 0:
                        logger.warning(f'{buyer_info.phone} 商品:{product_id} 库存不足')
                        page.locator("form path").nth(1).click()
                        page.wait_for_timeout(7_00)
                        continue
                    if button_quantity.get('currentItems') >= 1:
                        logger.info(f'{buyer_info.phone} 商品:{product_id} 已在购物车 单价:')
                        new_ozon_items.append(ozon_items[i])
                        page.locator("form path").nth(1).click()
                        page.wait_for_timeout(7_00)
                        continue
                    page.click(f'text={button_quantity.get("text")}')
                    logger.info(f'{buyer_info.phone} 商品:{product_id} 加入购物车 单价:')
                    page.wait_for_load_state("networkidle")
                    new_ozon_items.append(ozon_items[i])
                    page.wait_for_timeout(1_000)
                    page.locator("form path").nth(1).click()
                    page.wait_for_timeout(7_00)

    ozon_items = new_ozon_items
    #购物车
    if page.locator('div[data-widget="searchBarDesktop"] > div').count() == 2:
        page.mouse.click(2, 3)
        page.wait_for_timeout(7_00)
    page.goto("https://www.ozon.ru/cart", timeout=10000)  # 购物车
    page.wait_for_timeout(5000)
    page.get_by_role("button", name="Перейти к оформлению").click()  # 结算
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(2000)
    page.locator('div[style*="sberpay"]').click()
    page.wait_for_timeout(1000)
    page.get_by_role("button", name="Оплатить онлайн").click()  # 支付
    page.wait_for_timeout(10_000)
    # 查找特定元素
    page.screenshot(clip=page.locator("#root > div > div:nth-child(2)").bounding_box(),
                    path='C:/Users/<USER>/Downloads/fukuang.png')
    logger.info('等待付款200秒')
    page.wait_for_timeout(200_000)
    #点击返回按钮

    page.get_by_role("button", name="Вернуться на сайт").click()
    #等待URL变化 https://www.ozon.ru/my/order-done?orderNumber=0175716180-0004&showTabBar=false&number=0175716180-0004
    page.wait_for_timeout(10_000)
    order_id = [param.split('=')[1] for param in page.url.split('&')][0]
    order_done = page.locator('div[id^="state-orderDoneStatus"]').all()[0].get_attribute('data-state')
    money_str = json.loads(order_done).get('subtitle')[1].get('text', '')
    payment_amount = money_str.replace(' ', '').replace(' ','').replace('\xa0', '').replace(' ', '').replace('₽', '')
    logger.info(f'{buyer_info.phone} 订单号:{order_id} 下单数:{len(ozon_items)}')
    logger.info(f'预估金额:{budget_amount}₽ ,订单金额:{r_cart_price}₽ ,付款金额:{payment_amount}₽')
    for item in ozon_items:
        m_db['orders'].update_one({'taskId': item['taskId']}, {
            '$set': {'status': 3, 'orderId': order_id, 'price': item['price'], 'updateTime': datetime.now()}})
    # 转换为数字
    to_day_shopping[f"{buyer_info.phone}_ozon"] = 1
    return None


def buy_shop_item():
    proxy = ProxyServer("WLAN2", 1081)
    proxy.start()
    items = {}
    # 获取当前日期
    current_date = datetime.now().date()-timedelta(days=3)
    # 将日期与时间结合，设置时间为 00:00:00
    pre_date = datetime.combine(current_date, time(0, 0, 0))
    #todo
    orders_items = m_db['orders'].find(
        # {"status": 1,'buyer': '79801815917'},
        # {"status": 1,'buyer': {'$exists': True, '$ne': ''}, 'preDate':{'$lte': pre_date}},
        {"status": 1, 'buyer': {'$exists': True, '$ne': ''}},
        {'_id': 0, 'orderId': 1, 'keyword': 1, 'price': 1, 'buyer': 1, 'productId': 1, 'taskId': 1,
         'platform': 1}).sort('preDate', 1)
    orders_items = list(orders_items)
    for order in orders_items:
        if items.get(order.get('buyer')) is None:
            items[order.get('buyer')] = []
        items[order.get('buyer')].append(order)
    for buyer, item_list in items.items():
        wb_items = []
        ozon_items = []
        yandex_items = []
        for item in item_list:
            if item['platform'] == 'wb':
                wb_items.append(item)
            if item['platform'] == 'ozon':
                ozon_items.append(item)
            if item['platform'] == 'yandex':
                yandex_items.append(item)
        if len(ozon_items) > 0:
            sample_size = max(1, int(len(ozon_items) * 0.7)) if len(ozon_items) > 3 else random.choices([1, 2], weights=[0.3, 0.7])[0] if len(ozon_items) >= 2 else 1
            ozon_items = random.sample(ozon_items, min(sample_size, len(ozon_items)))
            if to_day_shopping.get(f"{buyer}_ozon",0) == 1:
                logger.info('买手:%s 今天ozon已经购物-跳过', buyer)
                continue
        if len(yandex_items) > 0:
            if to_day_shopping.get(f"{buyer}_yandex",0) == 1:
                logger.info('买手:%s 今天yandex已经购物-跳过', buyer)
                continue
            sample_size = max(1, int(len(yandex_items) * 0.7)) if len(yandex_items) > 3 else random.choices([1, 2], weights=[0.3, 0.7])[0] if len(yandex_items) >= 2 else 1
            yandex_items = random.sample(yandex_items, min(sample_size, len(yandex_items)))
        if len(wb_items) > 0:
            if to_day_shopping.get(f"{buyer}_wb",0) == 1:
                logger.info('买手:%s 今天WB已经购物-跳过', buyer)
                continue
            sample_size = max(1, int(len(wb_items) * 0.7)) if len(wb_items) > 3 else random.choices([1, 2], weights=[0.3, 0.7])[0] if len(wb_items) >= 2 else 1
            wb_items = random.sample(wb_items, min(sample_size, len(wb_items)))

        buyer_info = BuyerInfo(buyer, m_db)
        device, play = ip.change(buyer)
        page, close_client, playwright, browser, close_env, browser_context = open_browser(buyer_info)
        buy_ozon_item(ozon_items, page, buyer_info)
        buy_yandex_item(yandex_items, page)
        buy_wb_item(wb_items, page)

        browser.close()
        p_time.sleep(1)
        playwright.stop()
        p_time.sleep(1)
        close_env(container_code=buyer_info.device_id)


# jiesuan()
buy_shop_item()
