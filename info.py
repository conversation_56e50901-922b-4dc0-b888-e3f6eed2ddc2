import random
from db.client_mongdb import login_mongdb_review

class BuyerInfo:
    def __init__(self,phone,mdb=None):
        info = None
        if mdb is not None:
            info = mdb.buyer_info.find_one({"phone":phone})
        else:
            info = login_mongdb_review().buyer_info.find_one({"phone":phone})
        self._id = info.get('_id')
        self.email = info.get('email')
        self.emailPassword = info.get('emailPassword')
        self.emailUrl = info.get('emailUrl')
        self.phone = phone
        self.passportNumber = info.get('passportNumber')
        self.sex = info.get('sex')
        self.name = info.get('name')
        self.surname = info.get('surname')
        self.parentName = info.get('parentName')
        self.birthday = info.get('birthday')
        self.dateIssue = info.get('dateIssue')
        self.fmsCode = info.get('fmsCode')
        self.fmsName = info.get('fmsName')
        self.passportUrl = info.get('passportUrl')
        self.proxyCn = info.get('proxyCn')
        self.proxyGost = info.get('proxyGost')
        self.port = info.get('port')
        self.cdpPort = info.get('cdpPort')
        self.isDone = info.get('isDone')
        self.device_id = info.get('device_id')
        self.cardCSV = info.get('cardCSV')
        self.cardDate = info.get('cardDate')
        self.cardNum = info.get('cardNum')


class InfoTask:
    def __init__(self,taskInfo):

        self.taskId=taskInfo.get('taskId')
        self.preDate=taskInfo.get('preDate')
        self.preTime=taskInfo.get('preTime')
        self.orderId=taskInfo.get('orderId')
        self.buyer=taskInfo.get('buyer')
        self.productId=taskInfo.get('productId')
        self.img=taskInfo.get('img')
        self.keyword=taskInfo.get('keyword')
        self.ranking=taskInfo.get('ranking')
        self.price=taskInfo.get('price')
        self.like=taskInfo.get('like')
        self.cart=taskInfo.get('cart')
        self.order=taskInfo.get('order')
        self.qa=taskInfo.get('qa')
        self.review=taskInfo.get('review')
        self.review_img=taskInfo.get('review_img')
        self.review_video=taskInfo.get('review_video')
        self.taskInput=taskInfo.get('taskInput')

class ShuadanTask:
    def __init__(self,shuadanTask):
        self.taskId = shuadanTask['taskId']
        self.preDateTime =shuadanTask['preDateTime']
        self.like=shuadanTask['like']
        self.qa=shuadanTask['qa']
        self.cart=shuadanTask['cart']
        self.order=shuadanTask['order']
        self.pay=shuadanTask['pay']
        self.like_time=shuadanTask['like_time']
        self.qa_time=shuadanTask['qa_time']
        self.cart_time=shuadanTask['cart_time']
        self.order_time=shuadanTask['order_time']
        self.pay_time=shuadanTask['pay_time']
        self.page = shuadanTask['page']

class DailyMaintenanceTask:
    def __init__(self,dailyMaintenanceTask):
        self.taskId = dailyMaintenanceTask['taskId']
        self.keyword =dailyMaintenanceTask['keyword']
        self.buyer =dailyMaintenanceTask['buyer']
        self.preDateTime=dailyMaintenanceTask['preDateTime']
        self.isDone=dailyMaintenanceTask['isDone']
        self.doneTime=dailyMaintenanceTask['doneTime']


class ReviewTask:
    def __init__(self,reviewTask):
        self.taskId = reviewTask['taskId']
        self.review =reviewTask['review']
        self.review_img=reviewTask['review_img']
        self.review_video=reviewTask['review_video']
        self.review_time=reviewTask['review_time']


class TodoShuadanTask:
    def __init__(self,todoShuadanTask):
        self.taskId =todoShuadanTask['taskId']
        self.preDate=todoShuadanTask['preDate']
        self.preTime=todoShuadanTask['preTime']
        self.orderId=todoShuadanTask['orderId']
        self.buyer=todoShuadanTask['buyer']
        self.productId=todoShuadanTask['productId']
        self.img=todoShuadanTask['img']
        self.keyword=todoShuadanTask['keyword']
        self.ranking=todoShuadanTask['ranking']
        self.price=todoShuadanTask['price']
        self.like=todoShuadanTask['like']
        self.cart=todoShuadanTask['cart']
        self.order=todoShuadanTask['order']
        self.qa=todoShuadanTask['qa']
        self.review=todoShuadanTask['review']
        self.review_img=todoShuadanTask['review_img']
        self.review_video=todoShuadanTask['review_video']
        self.taskInput=todoShuadanTask['taskInput']

    def push(self):
        sd_infoTask= db['sd_infoTask']
        task = sd_infoTask.find_one({"taskId": self.taskId})
        if not task:
            sd_infoTask.insert_one(
                {
                    "taskId": self.taskId,
                    "preDate": self.preDate,
                    "preTime": self.preTime,
                    "orderId": self.orderId,
                    "buyer": self.buyer,
                    "productId": self.productId,
                    "img": self.img,
                    "keyword": self.keyword,
                    "ranking": self.ranking,
                    "price": self.price,
                    "like": self.like,
                    "cart": self.cart,
                    "order": self.order,
                    "qa": self.qa,
                    "review": self.review,
                    "review_img": self.review_img,
                    "review_video": self.review_video,
                    "taskInput": 'TRUE'
                }
            )
            self.taskInput = 'TRUE'
        else:
            print("已存在任务：",self.taskId)


def ziti_address():
    address_list= [
    "Москва, Большой Афанасьевский переулок, 35-37с4",
    "Москва, Большой Афанасьевский переулок, 36с1",
    "Москва, Малый Кисловский переулок, 9с1",
    "Москва, улица Новый Арбат, 11с1",
    "Москва, Малый Николопесковский переулок, 4",
    "Москва, Кривоарбатский переулок, 12",
    "Москва, переулок Каменная Слобода, 7",
    "Москва, Пречистенский переулок, 12",
    "Москва, Смоленский бульвар, 22/14",
    "Москва, Новоконюшенный переулок, 11",
    "Москва, улица Плющиха, 42",
    "г Москва, наб Тараса Шевченко, д. 1",
    "Москва, Большая Дорогомиловская улица, 8",
    "Москва, Брянская улица, 12",
    "Москва, набережная Тараса Шевченко 23А",
    "Москва, Кутузовский проспект, 27",
    "Москва, Кутузовский проспект, 26к1",
    "г Москва, ул Студенческая, д. 34",
    "Москва, Кутузовский проспект, 35",
    "Москва, Киевская улица, 20",]
    return random.choice(address_list)



if __name__ == '__main__':
    buyer = BuyerInfo( '7103')
    print(buyer.email)