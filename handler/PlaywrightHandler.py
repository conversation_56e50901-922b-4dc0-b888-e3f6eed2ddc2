#!/usr/bin/python
# -*- coding: UTF-8 -*-
import datetime
import json
import multiprocessing
import os
import time
import random
import re
from playwright.sync_api import Page
import requests
from sms import SMS
from info import BuyerInfo, InfoTask, ShuadanTask, ReviewTask,ziti_address
from redis import Redis
import traceback
red3 = Redis(host='************', port=6379, db=3, password="AirSmile@1688", )
s = SMS()
multiprocessing.freeze_support()




class Register():
    def __init__(self, page: Page, buyerInfo: BuyerInfo):
        self.page = page
        self.buyerInfo = buyerInfo

    def _capture_screenshot(self, func_name):
        # 使用当前日期和函数名作为文件名
        now = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        file_name = f"{func_name}_{now}.png"
        # 调用 Playwright API 来拍照并保存
        self.page.screenshot(path=file_name)

    def _capture_exception(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                print(f"Exception occurred in {func.__name__}: {e}")
                print(traceback.print_exc())
                args[0]._capture_screenshot(func.__name__)

        return wrapper

    @_capture_exception
    def finish_phoneNum_login(self):
        self.page.goto("https://www.ozon.ru/my/main")
        time.sleep(10)
        self.page.click("button:has-text(\"Войти или зарегистрироваться\")")
        time.sleep(10)
        iframe = self.page.query_selector('#authFrame').content_frame()
        time.sleep(2)
        iframe.locator('//input[@name="autocomplete"]').fill(s.getNumber())
        time.sleep(2)
        iframe.click("button:has-text(\"Войти\")")
        self.page.wait_for_timeout(5000)
        iframe.locator('//input[@name="otp"]').fill(s.getCode())
        iframe.locator('//label[@required="required"]/div[1]').click()
        iframe.click("button:has-text(\"Зарегистрироваться\")")
        while 1:
            self.page.wait_for_timeout(5000)
            if self.check_isDone_phoneNum_ligon():
                break

    @_capture_exception
    def check_isDone_phoneNum_login(self):
        account_count = self.page.locator('//div[@data-widget="userAvatar"]').count()
        if account_count != 0:
            account_name = self.page.locator('//div[@data-widget="userAvatar"]').locator('//span').nth(
                0).text_content().strip()
            account_name = account_name + " " + self.page.locator('//div[@data-widget="userAvatar"]').locator(
                '//span').nth(1).text_content().strip()
            print('【登陆成功，{}】'.format(account_name))
            print('账号已注册')
            return True

    @_capture_exception
    def binging_email(self):
        # 绑定邮箱
        # goto https://www.ozon.ru/ozonid
        self.page.goto('https://www.ozon.ru/ozonid')
        # 点击 添加邮箱 Добавить
        self.page.locator(
            "#layoutPage > div.b0 > div > section > div > div:nth-child(2) > ul > li:nth-child(2) > div > div > div > a").click()
        # 输入 page.fill('//input[@name="email"]')
        self.page.locator('//input[@name="email"]').fill(self.buyerInfo.email)
        self.page.click('button:has-text(\"Получить код\")')
        code = input("输入邮箱验证码")
        self.page.locator('//input[@name="otp"]').fill(code)
        self.page.wait_for_timeout(5000)


class Page_action():
    def __init__(self, page: Page,browser_context, buyerInfo: BuyerInfo, taskDB, db):
        self.page = page
        self.browser_context = browser_context
        self.find_done = False
        self.buyerInfo = buyerInfo
        self.taskInfo: InfoTask = "InfoTask"
        self.DB = db
        self.db = taskDB
        self._on_all()
        self.last_storage_time = None  # 初始化时未执行过
        global db_copy
        db_copy = self.db

    def _capture_screenshot(self, func_name):
        # 使用当前日期和函数名作为文件名
        now = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        file_name = f"{func_name}_{now}.png"
        # 调用 Playwright API 来拍照并保存
        self.page.screenshot(path=file_name)
        # #保存cookies
        # cookies_filename = '../cookies/{}'.format(self.buyerInfo.port)
        # os.makedirs(os.path.dirname(cookies_filename), exist_ok = True)
        # storage = self.browser_context.storage_state()
        # path = cookies_filename+"/end_{}.json".format(now)
        # with open(path, "w") as f:
        #     f.write(json.dumps(storage))
        #     print('cookies保存到{}'.format(path))


    def _capture_exception(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                print(f"Exception occurred in {func.__name__}: {e}")
                print(traceback.print_exc())
                args[0]._capture_screenshot(func.__name__)
                if func.__name__ == "pay_one":
                    db_copy.modify_shuadanTask_error('pay')
                if func.__name__ == "order_one":
                    db_copy.modify_shuadanTask_error('order')
                if func.__name__ == "add_cart":
                    db_copy.modify_shuadanTask_error('cart')
                if func.__name__ == "like_item":
                    db_copy.modify_shuadanTask_error('like')
                if func.__name__ == "ask_question":
                    db_copy.modify_shuadanTask_error('qa')

        return wrapper


    def _capture_on(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                print(f"监听异常 {func.__name__}: {e}")
        return wrapper

    @_capture_exception
    def check_login(self):
        """
        检查是否登陆账号
        """
        self.page.goto("https://www.ozon.ru/my/main")
        time.sleep(5)
        account_count = self.page.locator('span:has-text(\"Войти\")').count()
        if account_count == 0:
            # account_name = self.page.locator('//div[@data-widget="userAvatar"]').locator('//span').nth(
            #     0).text_content().strip()
            # account_name = account_name + " " + self.page.locator('//div[@data-widget="userAvatar"]').locator(
            #     '//span').nth(1).text_content().strip()
            print('【登陆成功，{}】'.format(self.buyerInfo.port))
            return True
        else:
            try:
                self.db.modify_shuadanTask_unlogin()
            except:
                self.db.modify_DailyMaintenanceTask_error()
            print('账号未登录')
            return False

    @_capture_exception
    def search_word(self, word):
        """
        搜索关键词
        """
        print('==========ACTION搜索关键开始==========')
        print('填充搜索关键词:path()====>{}'.format('//input[@placeholder="Искать на Ozon"]',word))
        self.page.locator('//input[@placeholder="Искать на Ozon"]').fill(word)
        print('点击搜索关键词:path()'.format('//div[@data-widget="searchBarDesktop"]//button[@aria-label="Поиск"]'))
        self.page.locator('//div[@data-widget="searchBarDesktop"]//button[@aria-label="Поиск"]').click()
        print('等待网页加载:状态为()'.format('load'))
        self.page.wait_for_load_state(state="load")
        print('完成搜索网页加载')
        print('==========ACTION搜索关键结束==========')

    @_capture_exception
    def random_click_item(self, word, click_item_quantity=1, like_item_quantity=0, addCart_item_quantity=0,
                          isOrder=False):
        """
        随机点击item
        click_item_quantity:点击item数量
        """
        self.order_product_id = None
        self.search_word(word)
        # 生成点赞的index序列
        like_item_quantity_list = []
        for i in range(0, like_item_quantity):
            random_num = random.randint(1, click_item_quantity)
            like_item_quantity_list.append(random_num - 1)
        print("第几个连接点击收藏:",like_item_quantity_list)
        # 生成加购的index序列
        addCart_item_quantity_list = []
        for i in range(0, addCart_item_quantity):
            random_num = random.randint(1, click_item_quantity)
            addCart_item_quantity_list.append(random_num - 1)
        print("第几个连接点击加购:",addCart_item_quantity_list)

        for i in range(0, click_item_quantity):
            time.sleep(1)
            items_list = self.page.locator('//div[@data-widget="searchResultsV2"]/div/div')
            counts = items_list.count()
            p = 0
            l = 0
            self.page_number =2
            while True:
                element_top = items_list.nth(random.randint(0, counts))
                if element_top.count()>0:
                    price_list = re.findall(r'([0-9]+?(?=₽))', element_top.inner_html().replace('\u2009', ''))
                    print('price_list:',price_list)
                    if len(price_list) == 0:
                        print('换一个element_top')
                    elif len(price_list)== 1:
                        min_value = price_list[0]
                    elif len(price_list) >= 2:
                        price_list = price_list[0-1]
                        try:
                            min_value = min(map(int, price_list))
                        except Exception as e:
                            print(e)
                    else:
                        try:
                            min_value = min(map(int, price_list))
                        except Exception as e:
                            print(e)
                    if min_value > 700:
                        break
                    else:
                        print('价格太低,重新选择listing')
                        if p >100:
                            p = 0
                            self.item_lisi_next_page_button()
                        p += 1
                else:
                    if l > 200:

                        break
                    l += 1
                    print('找不到element_top元素')
                    time.sleep(0.5)
            element = element_top.locator('a').nth(1)

            if element.count() > 0:
                element.click()
                print('进入click_item：{}  {}'.format(i, self.page.url))
                # self.page.wait_for_load_state(state="domcontentloaded")
                if i in like_item_quantity_list:
                    self.like_item()
                if i in addCart_item_quantity_list:
                    self.add_cart_day()
                # self.page.wait_for_load_state(state="domcontentloaded")
                self.page.keyboard.press(" ")
                time.sleep(random.randint(1, 4))
                self.page.keyboard.press(" ")
                time.sleep(random.randint(1, 3))
                self.page.go_back()
                # self.page.wait_for_load_state(state="domcontentloaded")
            else:
                print('找不到元素')
        if isOrder == True and self.order_product_id != None:
            self.order_one(self.order_product_id)

    @_capture_exception
    def random_click_item_case(self, word, click_item_quantity=1, like_item_quantity=0, addCart_item_quantity=0,
                          isOrder=False):
        """
        随机点击item
        click_item_quantity:点击item数量
        """
        self.order_product_id = None
        fbs_sku_list = self.DB['lib_product_data'].distinct('fbs_sku')
        self.search_word(word)
        # 生成点赞的index序列
        like_item_quantity_list = []
        for i in range(0, like_item_quantity):
            random_num = random.randint(1, click_item_quantity)
            if random_num-1 not in like_item_quantity_list:
                like_item_quantity_list.append(random_num - 1)
        print("第几个连接点击收藏:", like_item_quantity_list)
        # 生成加购的index序列
        addCart_item_quantity_list = []
        for i in range(0, addCart_item_quantity):
            random_num = random.randint(1, click_item_quantity)
            if random_num-1 not in addCart_item_quantity_list:
                addCart_item_quantity_list.append(random_num - 1)
        print("第几个连接点击加购:", addCart_item_quantity_list)
        self.sku_done =[]
        for i in range(0, click_item_quantity):
            self.page.wait_for_timeout(2000)
            items_list = self.page.locator('//div[@data-widget="searchResultsV2"]/div/div')
            counts = items_list.count()
            p = 0
            l = 0
            o = 1
            self.page_number =10
            while True:
                element_top = items_list.nth(o)
                if element_top.count() > 0:
                    while True:
                        htmltext =  element_top.inner_html()
                        skus = re.findall(r"\b\d{9,}\b",htmltext)
                        if skus != []:
                            sku = skus[0]
                            break
                        else:
                            l += 1
                            if l >100:
                                sku = '00000'
                                break

                    if int(sku) in fbs_sku_list and 'advert' not in htmltext and sku not in self.sku_done:
                        self.sku_done.append(sku)
                        break
                    else:
                        o += 1
                if o == 36:
                    self.item_lisi_next_page_button()
                    time.sleep(2)
                    o = 1

            element = element_top.locator('a').nth(1)

            if element.count() > 0:
                element.click()
                print('进入click_item：{}  {}'.format(i, self.page.url))
                # self.page.wait_for_load_state(state="domcontentloaded")
                if i in like_item_quantity_list:
                    self.like_item()
                if i in addCart_item_quantity_list:
                    self.shopName = self.DB['lib_product_data'].find_one({'fbs_sku':int(sku)},{'shopName':1,"_id":0})['shopName']
                    self.add_cart_day()
                # self.page.wait_for_load_state(state="domcontentloaded")
                self.page.keyboard.press(" ")
                time.sleep(random.randint(1, 4))
                self.page.keyboard.press(" ")
                time.sleep(random.randint(1, 3))
                self.page.go_back()
                # self.page.wait_for_load_state(state="domcontentloaded")
            else:
                print('找不到元素')
        if isOrder == True and self.order_product_id != None:
            self.order_one(self.order_product_id)


    def action_item(self, word, itemId, isLike=False, isAddcart=False, ask=''):
        self.page_number = self.DB['sd_todoShuadanTask'].find_one({'taskId': self.db.taskId})['page']
        self.productId = itemId

        self.search_word(word)
        if self.click_target_item(itemId):
            if isLike:
                self.like_item()
            if isAddcart:
                self.add_cart()
            if ask != '':
                self.ask_question(ask)

    def _click_target_item(self, itemId):
        """
        在当前页寻找并点击目标item
        """
        print('==========ACTION分析当前页面itemid:{}开始=========='.format(itemId))
        # self.page.wait_for_load_state(state='networkidle')
        MAGHOME = re.findall('MAGHOME', self.page.inner_html('//body'))
        if len(MAGHOME) > 0:
            print('找到了MAGHOME')
        PRODUCTID = re.findall(self.taskInfo.productId, self.page.inner_html('//body'))
        if len(PRODUCTID) > 0:
            print('找到了产品id:{}'.format(self.taskInfo.productId))
        items_list = self.page.locator('//div[@data-widget="searchResultsV2"]/div/div')
        counts = items_list.count()
        index = None
        for i in range(0, counts):
            url = items_list.nth(i).locator('a').nth(
                1).get_attribute('href')
            search_id = re.search('-\d*\/', url).group()
            search_id = search_id.replace('-', '').replace('/', '')

            if search_id in [itemId, str(int(itemId) - 1)]:
                if 'advert' not in url:  # 判断是否是广告连接
                    index = i
                else:
                    print('目标为广告链接:',url)

        if type(index) == int:
            items_list.nth(index).locator('a').nth(1).click()
            self.page.wait_for_load_state(state="domcontentloaded")
            print('==========ACTION分析当前页面itemid:{}结束=========='.format(itemId))
            print("进入详情页面，itemId：{}".format(itemId),"===>",self.page.url)
            self.find_done = True
            return True
        else:
            print('==========ACTION分析当前页面itemid:{}结束=========='.format(itemId))
            print('未找到目标item')

        # adv_count = self.page.locator('//div[@data-widget="skuAdvSearchShelf"]').count()
        # if adv_count > 0:
        #     for i in range(0, adv_count):
        #         items_list2 = self.page.locator('//div[@data-widget="skuAdvSearchShelf"]').nth(i)
        #         for u in range(0, items_list2.locator('//a[@target="_self"]').count()):
        #             url = items_list2.locator('//a[@target="_self"]').nth(u).get_attribute('href')
        #             search_id = re.search('\d{9}', url).group()
        #             if search_id in [itemId, str(int(itemId) - 1)]:
        #                 # if 'advert' not in url:  # 判断是否是广告连接
        #                 items_list2.locator('//a[@target="_self"]').nth(u).click()
        #                 self.page.wait_for_load_state(state="domcontentloaded")
        #                 print("进入详情页面，itemId：{}".format(itemId))
        #                 self.find_done = True
        #                 return True
        #
        # # adv_skuShelfGoods_count = self.page.locator('//div[@data-widget="skuShelfGoods"]').count()
        # adv_skuShelfGoods_count = 0
        # if adv_skuShelfGoods_count == 1:
        #     items_list3 = self.page.locator('//div[@data-widget="skuShelfGoods"]/div/div/div')
        #     item_count = items_list3.count()
        #     for o in range(0, item_count):
        #         item = items_list3.nth(o)
        #         url = item.locator('a').nth(1).get_attribute('href')
        #         search_id = re.search('\d{9}', url).group()
        #         if search_id in [itemId, str(int(itemId) - 1)]:
        #             # if 'advert' not in url:  # 判断是否是广告连接
        #             item.click()
        #             self.page.wait_for_load_state(state="domcontentloaded")
        #             print("进入详情页面，itemId：{}".format(itemId))
        #             self.find_done = True
        #             return True

        return False
        # items_list2 = self.page.locator('//div[@data-widget="skuShelfGoods"]/div[2]/div/div/div')

    @_capture_exception
    def click_target_item(self, itemId):
        """
        寻找目标item，并点击进入详情页面
        """
        # self._resp(itemId)
        print('===ACTION寻找目标itemid:{}开始==='.format(itemId))
        time.sleep(10)
        self.find_done = False
        if self._click_target_item(itemId) != True:
            self.item_lisi_next_page_button()
        while True:
            if self.find_done == True:
                if 'www.ozon.ru/product/' in self.page.url:
                    print('===ACTION寻找目标itemid:{}结束==='.format(itemId))
                    return True
                if self._click_target_item(itemId) == False:
                    self.item_lisi_next_page_button()
                else:
                    print('==========ACTION寻找目标itemid:{}结束=========='.format(itemId))
                    return True
            else:
                self.item_lisi_next_page_button()
                time.sleep(5)
                self._click_target_item(itemId)

    @_capture_exception
    def item_lisi_next_page_button(self):
        """
        翻页
        """
        exists = self.page.locator('div:text(\"Дальше\")').count()

        if exists == 1:
            # 翻页按钮型
            #抓取翻页按钮的数据
            parent_locator = self.page.locator('xpath=//div[text()="Дальше"]/../../..')
            a_tag = parent_locator.locator('a')

            #获取到下一页的url
            pageNumbers = {}#实际显示的页码储存
            for i in range(a_tag.count()):

                pageNumber = a_tag.nth(i).text_content()  # 实际显示的页码
                if pageNumber != 'Дальше' and pageNumber != None and pageNumber != '1' and pageNumber != '...' and pageNumber != 'В начало':
                    url = a_tag.nth(i).get_attribute('href')
                    pageUrlNumber = url.split('page=')[1].split('&')[0]  # Url上的页码
                    pageNumbers.update({int(pageNumber):url})
            if self.page_number in list(pageNumbers.keys()):
                #在列表中,进入列表的url
                willdoUrl = pageNumbers[self.page_number]

            else:
                # 判断self.page_number是否在该区间,如果不是,点击最大的那个
                willdoUrl = pageNumbers[ max(list(pageNumbers.keys()))]

            o = 1
            while o <= random.randint(1, 3):
                self.page.keyboard.press(' ')
                self.page.wait_for_timeout(random.randint(1000, 3000))
                o += 1

            self.page.goto("https://www.ozon.ru"+willdoUrl)
            print('进入{}'.format("https://www.ozon.ru"+willdoUrl))
            self.page.wait_for_load_state(state="domcontentloaded")
            self.page_number += 1
            print('{}翻页成功'.format(self.page_number))
            self.DB['sd_todoShuadanTask'].update_one({'taskId': self.db.taskId}, {'$set': {"page": self.page_number}})
        else:
            self.page.keyboard.press(' ')
            self.page.keyboard.press(' ')
            self.page.keyboard.press(' ')
            self.page.keyboard.press(' ')
            self.page.keyboard.press(' ')



    @_capture_exception
    def like_item(self):
        """
        点赞
        """
        # 判断当前是否在详情页
        if re.search("ozon.ru/product", self.page.url):
            self.page.locator('//div[@data-widget="webAddToFavorite"]').nth(0).click()
            print('点赞', self.page.url)
            self.db.modify_shuadanTask_done('like')
            return True

    @_capture_exception
    def ask_question(self, question):
        """
        QA提问
        """
        self.page.locator('//div[@data-widget="webQuestionCount"]').nth(0).click()
        self.page.wait_for_selector('p:text(\"Напишите свой вопрос\")')
        self.page.locator('//div[@data-widget="paginator"]//form[@novalidate="novalidate"]//p').fill(question)
        self.page.locator('//div[@data-widget="webCreateQuestion"]/form/div[3]').click()
        self.page.locator("button:has-text(\"Отправить вопрос\")").click()
        self.db.modify_shuadanTask_done('qa')

    def _on_all(self):
        self.inorderpage = 0
        self.isorderpagedone = 0
        self.start_esc_time = datetime.datetime.now()
        self.page.on("dialog", self._handle_alert_popup)
        self.page.on("domcontentloaded", self._handle_alert_popup)
        # self.page.on('response', lambda response: self._on_response_fill_addresses(response))
        # self.page.on('response', lambda response: self._on_esc(response))
        self.page.on('response', lambda response: self._on_save_cookies(response))
        self.page.on('response', lambda response: self._on_response_fill_name(response))
        self.page.on('response', lambda response: self._resp_unavailable_default_address(response))
        self.page.on('response', lambda response: self._resp_unavailable_default_address2(response))
        self.page.on('response', lambda response: self._resp_order(response))
        self.page.on('response', lambda response: self._resp_order_isdone(response))


    @_capture_on
    def _on_save_cookies(self, response):
        if response.status == 200 and '.js' not in response.url and '.woff2' not in response.url:
            now = datetime.datetime.now()

            # 检查是否已过30秒
            if self.last_storage_time is None or (now - self.last_storage_time).total_seconds() >= 30:
                cookies_dir = os.path.join('..', 'cookies', str(self.buyerInfo.port))
                os.makedirs(cookies_dir, exist_ok=True)

                now_str = now.strftime("%Y-%m-%d_%H-%M-%S.%f")[:-3]
                filename = f"end_{now_str}.json"
                path = os.path.join(cookies_dir, filename)

                files = [f for f in os.listdir(cookies_dir) if f.endswith('.json')]
                if len(files) >= 50:
                    oldest_file = min(files, key=lambda x: os.path.getctime(os.path.join(cookies_dir, x)))
                    os.remove(os.path.join(cookies_dir, oldest_file))

                storage = self.browser_context.storage_state()  # 获取当前浏览器的存储状态
                with open(path, "w") as f:
                    f.write(json.dumps(storage))
                    print(f'cookies保存到{path}')

                self.last_storage_time = now  # 更新最后执行时间

    @_capture_on
    def _on_esc(self,response):
        if '.js' not in response.url \
                and 'https://xapi.ozon.ru/dlte/multi' not in response.url \
                and '.woff2' not in response.url \
                and '.css' not in response.url \
                and '.jpg' not in response.url \
                and '.png' not in response.url \
                and '.svg' not in response.url:
            self.start_esc_time = datetime.datetime.now()
            url_old = self.page.url
            self.page.wait_for_timeout(30000)
            while True:
                self.page.wait_for_timeout(30000)
                if url_old == self.page.url:
                    # 按下Esc键
                    self.page.keyboard.press('Escape')
                    self.page.wait_for_timeout(10)
                    # 释放Esc键
                    self.page.keyboard.up('Escape')
                    print('url_old:',url_old )
                    print('按了下Esc键',self.page.url)
                else:
                    break




    @_capture_on
    def _resp_unavailable_default_address(self,response):
        if "https://www.ozon.ru/gocheckout/addressbook?ab_reason=unavailable_default_address" in response.url:
            print('[地址],检测到地址错误')
            self.page.wait_for_timeout(5000)
            if self.page.locator('//div[@vertical="checkoutFacade"]').count() > 0:
                print('[地址],判断是否存在可用地址')
                if self.page.locator('text=Адрес подходит для всего заказа').count()>0:
                    print('[地址],是存在可用地址,并点击该地址')
                    self.page.locator('text=Адрес подходит для всего заказа').click()
                else:
                    print('[地址],不存在可用地址,点击地址选择')
                    self.page.click('text=Добавить пункт выдачи, постамат или адрес доставки')

    @_capture_on
    def _resp_unavailable_default_address2(self,response):

        isurl = re.findall(r'https://www.ozon.ru/gocheckout/delivery?.*no_addresses', response.url)
        if "https://www.ozon.ru/gocheckout/delivery?addrbookid=0" in response.url or len(isurl) >= 1:
            print('[地址],等待8秒')
            time.sleep(8)
            print('[地址],检测到地址选择界面')
            if self.page.locator('//div[@label="Искать на карте"]//label').count() >=1:
                self.select_address_ziti(ziti_address())
            elif self.page.locator('//div[@label="Адрес доставки"]//label').count()>=1:
                res = requests.post('https://www.meiguodizhi.com/api/v1/dz',
                                    json={"path": "/ru-address/hot-city-Moscow", "method": "address"})
                address = json.loads(res.text)['address']['Address'].split('Москва')[0]
                self.select_address(address)

    @_capture_on
    def _resp_order(self,response):
        if response.status == 200 and 'https://www.ozon.ru/gocheckout' in self.page.url:
            if self.page.locator('text=ДОСТАВКА ПАРТНЁРОВ').count()>0:
                self.inorderpage += 1
                if self.inorderpage == 1:
                    print('[下单界面],检测到地址选择界面,等待8秒')
                    self.page.wait_for_timeout(8000)
                    if self.page.locator('//div[@data-widget="paymentInfoV2"]').locator(
                            "text={}".format("** " + self.buyerInfo.cardNum[-6:-1].replace(' ', ''))).count() > 1:
                        self.page.locator('//div[@data-widget="paymentInfoV2"]').locator(
                            "text={}".format("** " + self.buyerInfo.cardNum[-6:-1].replace(' ', ''))).click()
                    elif self.page.locator('//div[@data-widget="paymentInfoV2"]').locator("text=Картой онлайн").count() > 1:
                        self.page.locator('//div[@data-widget="paymentInfoV2"]').locator("text=Картой онлайн").click()
                    elif self.page.locator('//div[@data-widget="paymentInfoV2"]').locator("text=Новой картой").count() > 1:
                        self.page.locator('//div[@data-widget="paymentInfoV2"]').locator("text=Новой картой").click()
                    self.page.wait_for_timeout(10000)
                    print('[下单界面],点击确认下单按钮')
                    self.page.locator('//div[@data-widget="stickyContainer"]').locator("text=Оплатить онлайн").nth(0).click()
                    print('[下单界面],点击完成,等待10秒')
                    self.page.wait_for_timeout(10000)


    @_capture_on
    def _resp_order_isdone(self,response):
        # 判断是否下单成功
        if 'https://www.ozon.ru/payment_form?number=' in self.page.url or 'https://www.ozon.ru/thank_you/0121177240-0003?number=' in self.page.url:
            self.isorderpagedone += 1
            if self.isorderpagedone == 1:
                orderId = self.page.url.split('=')[-1]
                self.db.modify_shuadanTask_done('order')
                self.db.modify_infoTask_orderId(orderId)
                print('下单成功', orderId)
                red3.hset('OZON_ORDER_BRUSH', orderId, datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                self.order_done_id = orderId

    def _handle_alert_popup(self):
        # 如果选择器出现，那么你可以在这里添加你的逻辑
        try:
            time.sleep(1)
            if self.page.locator('//div[@data-widget="alertPopup"]').count() == 1:
                print('有弹窗')
                # self.page.locator('//div[@data-widget="alertPopup"]').locator('//button').nth(1).click()
                print(self.page.locator('//svg[@xmlns="http://www.w3.org/2000/svg"]').count())
        except:
            text = self.page.inner_html('//body')
            res = re.search('无法访问此网站', text)
            if res:
                raise Exception('无法访问此网站')
            print('点击弹窗异常')


    @_capture_on
    def _on_response_fill_name(self, response):
        """
        通过on_response方法拦截Ajax请求，直接获取响应结果。
        """

        if "www.ozon.ru/gocheckout?start=0" in response.url:
            print('判断连接fill_name：', response.url)

            if response.status != 200:
                pass
            else:
                try:
                    self.Action_fill_name = True
                    iframe = self.page.query_selector('#authFrame').content_frame()
                    if iframe.locator('//div[@id="patchUserAccountV2"]').count() > 0:
                        iframe.fill('//input[@name="firstName"]', self.buyerInfo.name)
                        iframe.fill('//input[@name="lastName"]', self.buyerInfo.surname)
                        iframe.locator("span:has-text(\"Продолжить\")").nth(1).click()
                        time.sleep(10)
                except:
                    print('已经填写名字')

    def _on_response_fill_addresses(self, response):
        """
        通过on_response方法拦截Ajax请求，直接获取响应结果。
        """
        if "https://www.ozon.ru/gocheckout?start=0" in response.url:
            if self.page.locator('//div[@data-widget="blockVertical"]').count() > 0:
                print('获取到地址选择')


    def _fill_name(self):
        time.sleep(5)
        try:
            self.Action_fill_name = True
            iframe = self.page.query_selector('#authFrame').content_frame()
            if iframe.locator('//div[@id="patchUserAccountV2"]').count() > 0:
                iframe.fill('//input[@name="firstName"]', self.buyerInfo.name)
                iframe.fill('//input[@name="lastName"]', self.buyerInfo.surname)
                iframe.locator("span:has-text(\"Продолжить\")").nth(1).click()
                time.sleep(10)
        except:
            print('已经填写名字')

    def _fill_addresses(self):
        try:
            self.page.click("span:has-text(\"Курьером\")")
            self.page.wait_for_timeout(5000)
            if self.page.locator('text=Способ доставки').count() > 0:
                while 1:
                    res = requests.post('https://www.meiguodizhi.com/api/v1/dz',
                                        json={"path": "/ru-address/hot-city-Moscow", "method": "address"})
                    address = json.loads(res.text)['address']['Address'].split('Москва')[0]
                    if res.status_code == 200:
                        self.select_address(address)

        except:
            print("填写地址出错")

    def _on_response(self, response, itemId):
        """
        通过on_response方法拦截Ajax请求，直接获取响应结果。
        """
        if "api/entrypoint-api.bx/page/json/v2?url=" in response.url:
            print('判断连接', response.url)
            if response.status != 200:
                pass
            else:
                # print(response.json())
                res = re.search(itemId, response.text())
                if res:
                    self.find_done = True
                print('接收正常')




    @_capture_exception
    def add_cart(self):
        """
        添加购物车
        """
        self.page.locator('//div[@data-widget="webAddToCart"]//button').nth(0).click()
        time.sleep(5)
        if not 'https://www.ozon.ru/cart/' in self.page.url:
            self.page.locator('//div[@data-widget="webAddToCart"]//button').nth(0).click()
            self.page.wait_for_timeout(10000)
            print('加购', self.page.url)
            # self.page.go_back()
        else:
            print('加购', self.page.url)
            self.page.wait_for_timeout(10000)

        self.db.modify_shuadanTask_done('cart')
        self.order_product_id  = self.page.url.replace('https://www.ozon.ru/cart/#cartItem=','')
        # self.page.go_back()


    @_capture_exception
    def add_cart_day(self):
        """
        添加购物车
        """
        self.page.locator('//div[@data-widget="webAddToCart"]//button').nth(0).click()
        time.sleep(5)
        if not 'https://www.ozon.ru/cart/' in self.page.url:
            self.page.locator('//div[@data-widget="webAddToCart"]//button').nth(0).click()
            self.page.wait_for_timeout(10000)
            print('加购', self.page.url)
            # self.page.go_back()
        else:
            print('加购', self.page.url)

        # self.db.modify_shuadanTask_done('cart')
        self.order_product_id  = self.page.url.replace('https://www.ozon.ru/cart/#cartItem=','')
        self.page.go_back()


    @_capture_exception
    def order_one(self, id):
        """
        下订单：进入购物车后，选择第一个产品下单
        """
        self.order_done_id = None
        self.page.goto('https://www.ozon.ru/product/x-{}'.format(id), wait_until="domcontentloaded")
        title = self.page.locator('//div[@data-widget="webProductHeading"]').text_content()
        self.page.goto('https://www.ozon.ru/cart', wait_until="domcontentloaded")
        self.page.wait_for_timeout(5000)
        while self.page.locator('//div[@data-widget="controls"]').locator('text=Удалить выбранные').count() > 0:
            self.page.locator('//div[@data-widget="controls"]').locator('text=Выбрать все').click()  # 点击取消全选
            self.page.wait_for_timeout(5000)

        self.page.wait_for_timeout(3000)
        for i in range(1, self.page.locator('//div[@data-widget="split"]/div').count()):
            if title in self.page.locator('//div[@data-widget="split"]/div').nth(i).text_content():
                self.page.locator('//div[@data-widget="split"]/div').nth(i).locator('//label').click()
                self.page.wait_for_timeout(5000)
                break

        self.page.click('button:has-text(\"Перейти к оформлению\")')
        try:
            self._fill_name()
            # self._fill_addresses()
        except:
            pass
        while True:
            t = 10
            self.page.wait_for_timeout(10000)
            t += 10
            if self.order_done_id != None or t > 180:
                print('order_one','完成')
                break
            else:
                print('等待下单完成')




    @_capture_exception
    def delete_cart_product(self):
        """
        清空购物车
        """
        try:
            while 1:
                self.page.goto('https://www.ozon.ru/cart', wait_until="domcontentloaded")
                time.sleep(1)
                if self.page.locator("span:has-text(\"Удалить\")").count() > 0:
                    self.page.locator("span:has-text(\"Удалить\")").nth(0).click()
                    time.sleep(2)
                    self.page.locator('//div[@class="vue-portal-target"]').locator(
                        'button:has-text(\'Удалить\')').click()
                else:
                    break
        except:
            print('删除购物车出错')

    # @_capture_exception
    def select_address(self, address):
        """
        输入一个地址，然后选择确定
        """
        while True:
            try:
                print('[到门地址],输入地址')
                self.page.locator('//div[@label="Адрес доставки"]//label').fill(address)
                self.page.wait_for_timeout(5000)
                print('[到门地址],选择第一个地址')
                self.page.locator('//div[@role="option"]').nth(0).click()
                self.page.wait_for_timeout(5000)
                print('[到门地址],确认地址1')
                self.page.locator("span:has-text(\"Привезти сюда\")").nth(1).click()
                self.page.wait_for_timeout(5000)
                print('[到门地址],确认地址2')
                self.page.locator("span:has-text(\"Сохранить и продолжить\")").nth(1).click()
                self.page.wait_for_timeout(5000)
                break
            except Exception as e:
                print(e)
                print('重新输入地址')
                self.page.reload()



    def select_address_ziti(self, address):
        """
        输入一个地址，然后选择确定
        """
        while True:
            try:
                print('[自提地址],输入地址')
                self.page.locator('//div[@label="Искать на карте"]//label').fill(address)
                self.page.wait_for_timeout(5000)
                print('[自提地址],选择第一个地址')
                self.page.locator('//div[@role="option"]').nth(0).click()
                self.page.wait_for_timeout(5000)
                print('[自提地址],确认地址')
                self.page.locator('//div[@data-widget="addressEditConfirmButton"]').click()
                self.page.wait_for_timeout(5000)
                break
            except Exception as e:
                print(e)
                print('重新输入地址')
                self.page.reload()






    @_capture_exception
    def pay_one(self):
        """
        付款：进入未支付订单界面，点击付款
        """
        self.page.goto('https://www.ozon.ru/my/orderlist')
        time.sleep(5)
        self.page.locator('//section[@data-widget="orderList"]/div[1]').locator('button:has-text(\"Оплатить\")').click()
        # self.page.locator('//div[@data-widget="paymentInfoV2"]').locator("text=Картой онлайн").click()
        # self.page.locator('//div[@data-widget="stickyContainer"]').locator('//div[@text="Оплатить онлайн"]').click()
        # self.page.locator('//div[@data-widget="stickyContainer"]').locator('//div[@text="Оплатить онлайн"]').nth(
        #     0).click()
        order_id = self.page.locator('//section[@data-widget="orderList"]/div[1]').locator('a').nth(0).text_content()
        self.page.goto('https://www.ozon.ru/payment_form?number=' + order_id)
        time.sleep(5)
        payIframeUrl = self.page.locator('//div[@data-widget="createPayment"]/iframe').get_attribute('src')
        print(payIframeUrl)
        self.page.goto(payIframeUrl)
        if self.page.locator('//div[@id="card-previews"]/div/ul/li').count() >= 2:
            for i in range(0, self.page.locator('//div[@id="card-previews"]/div/ul/li').count()):
                #如果找到正确的信用卡号就点击
                if self.buyerInfo.cardNum[-5:] in self.page.locator('//div[@id="card-previews"]/div/ul/li').nth(i).text_content():
                    self.page.click('//input[@qa-id="payButton"]')
                    time.sleep(10)

            if 'Новая карта' in self.page.locator(
                    '//div[@id="card-previews"]/div/ul/li').nth(-1).text_content():
                self.page.locator(
                    '//div[@id="card-previews"]/div/ul/li').nth(-1).click()
                self.page.wait_for_timeout(1000)
                self.page.locator('//input[@name="cc-number"]').fill(self.buyerInfo.cardNum.replace(" ", ''))
                self.page.locator('//input[@name="cc-exp-month"]').fill(self.buyerInfo.cardDate.split('/')[0])
                self.page.locator('//input[@name="cc-exp-year"]').fill(self.buyerInfo.cardDate.split('/')[1])
                self.page.locator('//input[@name="cc-csc"]').fill(self.buyerInfo.cardCSV)
                self.page.click('//input[@qa-id="payButton"]')
                time.sleep(10)

        else:
            self.page.locator('//input[@name="cc-number"]').fill(self.buyerInfo.cardNum.replace(" ", ''))
            self.page.locator('//input[@name="cc-exp-month"]').fill(self.buyerInfo.cardDate.split('/')[0])
            self.page.locator('//input[@name="cc-exp-year"]').fill(self.buyerInfo.cardDate.split('/')[1])
            self.page.locator('//input[@name="cc-csc"]').fill(self.buyerInfo.cardCSV)
            self.page.click('//input[@qa-id="payButton"]')
            time.sleep(20)

        if self.page.locator('text=Заказ оформлен и оплачен').count() >= 1:

            orderId = self.page.url.split('=')[1]
            payMoney = self.page.locator('//section[@data-widget="orderDoneDetailsWeb"]//b').nth(
                1).text_content().replace('\xa0', '')
            self.db.modify_shuadanTask_done('pay')
            self.db.modify_infoTask_payInfo(payMoney)
            print('付款成功', {"购买者": self.buyerInfo.port, "订单号": orderId, "订单金额": payMoney})
        elif self.page.locator('text=Заказ не оплачен').count() > 0:
            print('订单付款失败')
            raise '订单付款失败'

        elif self.page.locator('text=Укажите паспортные данные получателя').count() > 0:
            orderId = self.page.locator('//section[@data-widget="orderDoneDetailsWeb"]//b').nth(0).text_content()
            payMoney = self.page.locator('//section[@data-widget="orderDoneDetailsWeb"]//b').nth(
                1).text_content().replace('\xa0', '')
            self.db.modify_shuadanTask_done('pay')
            self.db.modify_infoTask_payInfo(payMoney)
            print('付款成功，需要护照', {"购买者": self.buyerInfo.port, "订单号": orderId, "订单金额": payMoney})

        # if self.page.is_dialog_open():
        #     # 获取弹窗对象并关闭弹窗
        #     dialog = self.page.dialog
        #     dialog.dismiss()

    @_capture_exception
    def login(self, buyerInfo):
        print('')
        self.page.goto("https://www.ozon.ru/my/main")
        time.sleep(3)
        self.page.click("button:has-text(\"Войти или зарегистрироваться\")")
        time.sleep(2)
        iframe = self.page.query_selector('#authFrame').content_frame()
        iframe.locator("text=Войти по почте").click()
        time.sleep(2)
        iframe.fill('//input[@name="email"]', buyerInfo.email)
        iframe.click('text=Получить код')
        code = input("输入邮箱验证码")
        iframe.locator('//input[@name="otp"]').fill(code)


class PageShuadanActionBase(Page_action):

    def like(self, taskInfo: InfoTask):
        self.taskInfo = taskInfo
        if 'https://www.ozon.ru/product/' in self.page.url:
            self.like_item()

        else:
            self.action_item(taskInfo.keyword, taskInfo.productId, isLike=True)

    def qa(self, taskInfo: InfoTask):
        if 'https://www.ozon.ru/product/' in self.page.url:
            self.ask_question(taskInfo.qa)
        else:
            self.action_item(taskInfo.keyword, taskInfo.productId, ask=taskInfo.qa)

    def cart(self, taskInfo: InfoTask):
        if 'https://www.ozon.ru/product/' in self.page.url:
            self.add_cart()
        else:
            self.action_item(taskInfo.keyword, taskInfo.productId, isAddcart=True)

    def order(self, taskInfo: InfoTask):
        self.taskInfo = taskInfo
        self.order_one(taskInfo.productId)

    def pay(self, taskInfo: InfoTask):
        self.taskInfo = taskInfo
        self.pay_one()

    def click_item(self, taskInfo: InfoTask):
        if 'чехол' in taskInfo.keyword:
            self.random_click_item_case(taskInfo.keyword, random.randint(3, 5),addCart_item_quantity=3,like_item_quantity=2,isOrder=False,)
            self.db.taskId = taskInfo.taskId
            self.db.modify_DailyMaintenanceTask_done(self.shopName)
        else:
            self.random_click_item(taskInfo.keyword, random.randint(1, 2),addCart_item_quantity=1,isOrder=True)
            self.db.taskId = taskInfo.taskId
            self.db.modify_DailyMaintenanceTask_done()



