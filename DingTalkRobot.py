import os
import time
import hmac
import hashlib
import base64
import urllib.parse
import requests
from dotenv import load_dotenv


class DingTalkRobot:
    def __init__(self):
        load_dotenv()
        self.access_token = "8d6dda43349d5a12d8c6fa5de3b26cb5c489f7809251d85fce3e6a2a5218bb7a"
        self.secret = "SECc89fb889b0a505ff11492d69927d7bac36d644e39dcdc2cb7a4a37a2a2870c33"
        self.base_url = "https://oapi.dingtalk.com/robot/send"

        if not self.access_token or not self.secret:
            raise ValueError("Missing DingTalk credentials in environment variables")

    def _generate_signature(self) -> dict:
        """生成加签参数"""
        timestamp = str(round(time.time() * 1000))
        secret_enc = self.secret.encode('utf-8')
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(secret_enc, string_to_sign.encode('utf-8'), digestmod=hashlib.sha256).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return {"timestamp": timestamp, "sign": sign}

    def send(self, message_type: str, content: dict, at_users: list = None, at_all: bool = False) -> dict:
        """发送消息到钉钉群"""
        params = {"access_token": self.access_token}
        params.update(self._generate_signature())

        payload = {
            "msgtype": message_type,
            message_type: content
        }

        if at_users or at_all:
            payload["at"] = {
                "atUserIds": at_users or [],
                "isAtAll": at_all
            }

        try:
            response = requests.post(
                self.base_url,
                params=params,
                json=payload,
                timeout=5
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"errcode": 500, "errmsg": f"Request failed: {str(e)}"}


# ======== 消息构造器 ========
class MessageBuilder:
    @staticmethod
    def text(content: str) -> dict:
        return {"content": content}

    @staticmethod
    def link(title: str, text: str, pic_url: str, message_url: str) -> dict:
        return {
            "title": title,
            "text": text,
            "picUrl": pic_url,
            "messageUrl": message_url
        }

    @staticmethod
    def markdown(title: str, text: str) -> dict:
        return {
            "title": title,
            "text": text
        }