#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
装箱标签SKU处理器
功能：读取Excel装箱数据，识别PDF标签中的二维码，匹配数据并添加SKU信息到PDF
"""

import pandas as pd
import fitz  # PyMuPDF
import cv2
import numpy as np
from pyzbar import pyzbar
from PIL import Image, ImageDraw, ImageFont
import io
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import sys

class PackingLabelProcessor:
    """装箱标签处理器"""
    
    def __init__(self, excel_file: str, pdf_file: str, output_file: str = None):
        """
        初始化处理器
        
        Args:
            excel_file: Excel装箱数据文件路径
            pdf_file: PDF标签文件路径
            output_file: 输出PDF文件路径，默认为原文件名_processed.pdf
        """
        self.excel_file = excel_file
        self.pdf_file = pdf_file
        self.output_file = output_file or self._generate_output_filename()
        
        # 数据存储
        self.packing_data = {}  # 装箱数据字典 {箱码: {sku, quantity, ...}}
        self.processing_log = []  # 处理日志
        
        # 统计信息
        self.stats = {
            'total_pages': 0,
            'qr_detected': 0,
            'matched_success': 0,
            'matched_failed': 0,
            'processing_errors': 0
        }
        
        # 设置日志
        self._setup_logging()
    
    def _generate_output_filename(self) -> str:
        """生成输出文件名"""
        pdf_path = Path(self.pdf_file)
        return str(pdf_path.parent / f"{pdf_path.stem}_processed{pdf_path.suffix}")
    
    def _setup_logging(self):
        """设置日志配置"""
        log_filename = f"packing_processor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("装箱标签处理器初始化完成")
    
    def load_excel_data(self) -> bool:
        """
        读取Excel装箱数据
        
        Returns:
            bool: 是否成功读取数据
        """
        try:
            self.logger.info(f"开始读取Excel文件: {self.excel_file}")
            
            # 读取Excel文件
            df = pd.read_excel(self.excel_file)
            
            # 验证必要的列是否存在
            required_columns = ['Баркод товара', 'Кол-во товаров', 'ШК короба']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                self.logger.error(f"Excel文件缺少必要的列: {missing_columns}")
                return False
            
            # 构建装箱数据字典
            for _, row in df.iterrows():
                box_code = str(row['ШК короба']).strip()
                sku = str(row['Баркод товара']).strip()
                quantity = int(row['Кол-во товаров'])
                
                self.packing_data[box_code] = {
                    'sku': sku,
                    'quantity': quantity,
                    'expiry_date': row.get('Срок годности', '')
                }
            
            self.logger.info(f"成功读取 {len(self.packing_data)} 条装箱数据")
            return True
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {str(e)}")
            return False
    
    def detect_qr_code(self, page) -> Optional[str]:
        """
        检测页面中的二维码
        
        Args:
            page: PDF页面对象
            
        Returns:
            str: 二维码内容，如果未检测到则返回None
        """
        try:
            # 转换页面为图像，使用2倍缩放提高识别率
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            img_data = pix.tobytes('png')
            
            # 转换为OpenCV格式
            pil_image = Image.open(io.BytesIO(img_data))
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            # 检测二维码
            qr_codes = pyzbar.decode(cv_image)
            
            if qr_codes:
                # 返回第一个二维码的内容
                qr_data = qr_codes[0].data.decode('utf-8', errors='ignore').strip()
                return qr_data
            
            return None
            
        except Exception as e:
            self.logger.error(f"二维码检测失败: {str(e)}")
            return None
    
    def add_sku_to_page(self, page, sku_info: Dict) -> bool:
        """
        在PDF页面上添加SKU信息
        
        Args:
            page: PDF页面对象
            sku_info: SKU信息字典
            
        Returns:
            bool: 是否成功添加
        """
        try:
            # 获取页面尺寸
            rect = page.rect
            
            # 准备要添加的文本
            sku_text = f"{sku_info['sku']}"
            
            # 设置文本位置（在二维码下方）
            text_x = 85  # 与二维码左对齐
            text_y = 255  # 二维码下方
            
            # 添加SKU文本
            page.insert_text(
                (text_x, text_y),
                sku_text,
                fontsize=22,
                color=(0, 0, 0),  # 黑色
                fontname="notosbo"
            )
            return True

        except Exception as e:
            self.logger.error(f"添加SKU信息失败: {str(e)}")
            return False

    def process_pdf(self) -> bool:
        """
        处理PDF文件，识别二维码并添加SKU信息

        Returns:
            bool: 是否成功处理
        """
        try:
            self.logger.info(f"开始处理PDF文件: {self.pdf_file}")

            # 打开PDF文件
            doc = fitz.open(self.pdf_file)
            self.stats['total_pages'] = len(doc)

            self.logger.info(f"PDF总页数: {self.stats['total_pages']}")

            # 逐页处理
            for page_num in range(len(doc)):
                page = doc[page_num]

                try:
                    # 检测二维码
                    qr_data = self.detect_qr_code(page)

                    if qr_data:
                        self.stats['qr_detected'] += 1
                        self.logger.debug(f"第 {page_num + 1} 页检测到二维码: {qr_data}")

                        # 查找匹配的装箱数据
                        if qr_data in self.packing_data:
                            sku_info = self.packing_data[qr_data]

                            # 添加SKU信息到页面
                            if self.add_sku_to_page(page, sku_info):
                                self.stats['matched_success'] += 1
                                self.processing_log.append({
                                    'page': page_num + 1,
                                    'qr_code': qr_data,
                                    'sku': sku_info['sku'],
                                    'quantity': sku_info['quantity'],
                                    'status': 'success'
                                })
                                self.logger.debug(f"第 {page_num + 1} 页成功添加SKU: {sku_info['sku']}")
                            else:
                                self.stats['processing_errors'] += 1
                                self.processing_log.append({
                                    'page': page_num + 1,
                                    'qr_code': qr_data,
                                    'status': 'add_sku_failed'
                                })
                        else:
                            self.stats['matched_failed'] += 1
                            self.processing_log.append({
                                'page': page_num + 1,
                                'qr_code': qr_data,
                                'status': 'no_match'
                            })
                            self.logger.warning(f"第 {page_num + 1} 页二维码 {qr_data} 未找到匹配的装箱数据")
                    else:
                        self.processing_log.append({
                            'page': page_num + 1,
                            'status': 'no_qr_detected'
                        })
                        self.logger.warning(f"第 {page_num + 1} 页未检测到二维码")

                except Exception as e:
                    self.stats['processing_errors'] += 1
                    self.processing_log.append({
                        'page': page_num + 1,
                        'status': 'error',
                        'error': str(e)
                    })
                    self.logger.error(f"处理第 {page_num + 1} 页时出错: {str(e)}")

                # 每处理50页输出一次进度
                if (page_num + 1) % 50 == 0:
                    self.logger.info(f"已处理 {page_num + 1}/{self.stats['total_pages']} 页")

            # 保存处理后的PDF
            doc.save(self.output_file)
            doc.close()

            self.logger.info(f"PDF处理完成，输出文件: {self.output_file}")
            return True

        except Exception as e:
            self.logger.error(f"处理PDF文件失败: {str(e)}")
            return False

    def generate_report(self) -> str:
        """
        生成处理报告

        Returns:
            str: 处理报告内容
        """
        report = []
        report.append("=" * 60)
        report.append("装箱标签SKU处理报告")
        report.append("=" * 60)
        report.append(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"输入文件: {self.excel_file}, {self.pdf_file}")
        report.append(f"输出文件: {self.output_file}")
        report.append("")

        # 统计信息
        report.append("处理统计:")
        report.append(f"  总页数: {self.stats['total_pages']}")
        report.append(f"  检测到二维码: {self.stats['qr_detected']}")
        report.append(f"  匹配成功: {self.stats['matched_success']}")
        report.append(f"  匹配失败: {self.stats['matched_failed']}")
        report.append(f"  处理错误: {self.stats['processing_errors']}")

        # 成功率
        if self.stats['total_pages'] > 0:
            success_rate = (self.stats['matched_success'] / self.stats['total_pages']) * 100
            report.append(f"  成功率: {success_rate:.2f}%")

        report.append("")

        # 详细日志（仅显示失败的情况）
        failed_logs = [log for log in self.processing_log if log['status'] != 'success']
        if failed_logs:
            report.append("失败详情:")
            for log in failed_logs[:20]:  # 最多显示20条失败记录
                page = log['page']
                status = log['status']
                if status == 'no_match':
                    qr_code = log.get('qr_code', 'N/A')
                    report.append(f"  第{page}页: 二维码 {qr_code} 无匹配数据")
                elif status == 'no_qr_detected':
                    report.append(f"  第{page}页: 未检测到二维码")
                elif status == 'error':
                    error = log.get('error', 'Unknown error')
                    report.append(f"  第{page}页: 处理错误 - {error}")
                elif status == 'add_sku_failed':
                    qr_code = log.get('qr_code', 'N/A')
                    report.append(f"  第{page}页: 二维码 {qr_code} 添加SKU失败")

            if len(failed_logs) > 20:
                report.append(f"  ... 还有 {len(failed_logs) - 20} 条失败记录")

        report.append("=" * 60)

        return "\n".join(report)

    def run(self) -> bool:
        """
        运行完整的处理流程

        Returns:
            bool: 是否成功完成处理
        """
        try:
            self.logger.info("开始装箱标签SKU处理流程")

            # 1. 读取Excel数据
            if not self.load_excel_data():
                self.logger.error("读取Excel数据失败，处理终止")
                return False

            # 2. 处理PDF文件
            if not self.process_pdf():
                self.logger.error("处理PDF文件失败，处理终止")
                return False

            # 3. 生成并保存报告
            report = self.generate_report()

            # 保存报告到文件
            report_filename = f"processing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report)

            # 输出报告到控制台
            print(report)

            self.logger.info(f"处理完成！报告已保存到: {report_filename}")
            return True

        except Exception as e:
            self.logger.error(f"处理流程失败: {str(e)}")
            return False


def main():
    """主函数"""
    # 文件路径配置
    order_id = "32257946"
    excel_file = f"file/wb/{order_id}-装箱.xlsx"
    pdf_file = f"file/wb/{order_id}-装箱-标签.pdf"
    output_file = f"file/wb/{order_id}-装箱-标签_processed.pdf"

    # 检查输入文件是否存在
    if not Path(excel_file).exists():
        print(f"错误: Excel文件不存在 - {excel_file}")
        return False

    if not Path(pdf_file).exists():
        print(f"错误: PDF文件不存在 - {pdf_file}")
        return False

    # 创建处理器并运行
    processor = PackingLabelProcessor(excel_file, pdf_file, output_file)

    print("装箱标签SKU处理器")
    print("=" * 50)
    print(f"Excel文件: {excel_file}")
    print(f"PDF文件: {pdf_file}")
    print(f"输出文件: {output_file}")
    print("=" * 50)

    success = processor.run()

    if success:
        print("\n✅ 处理成功完成！")
        return True
    else:
        print("\n❌ 处理失败，请查看日志了解详情。")
        return False


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断处理")
    except Exception as e:
        print(f"\n程序异常: {str(e)}")
        logging.error(f"程序异常: {str(e)}", exc_info=True)
