from typing import Optional
from datetime import datetime
from pymongo.database import Database

class BuyerInfo:
    """买家信息模型类"""
    
    def __init__(self, buyer_id: str, db: Database):
        """
        初始化买家信息
        
        Args:
            buyer_id: 买家ID
            db: MongoDB数据库实例
        """
        self.buyer_id = buyer_id
        self.db = db
        self._info = self._load_info()
        
    def _load_info(self) -> dict:
        """从数据库加载买家信息"""
        return self.db['buyers'].find_one({'_id': self.buyer_id}) or {}
        
    @property
    def device_id(self) -> int:
        """获取设备ID"""
        return self._info.get('device_id', 0)
        
    @property
    def last_login(self) -> Optional[datetime]:
        """获取最后登录时间"""
        return self._info.get('last_login')
        
    def update_last_login(self):
        """更新最后登录时间"""
        self.db['buyers'].update_one(
            {'_id': self.buyer_id},
            {'$set': {'last_login': datetime.now()}}
        )
        self._info['last_login'] = datetime.now() 