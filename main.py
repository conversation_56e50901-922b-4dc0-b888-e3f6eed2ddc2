import logging
import random
from datetime import datetime, timedelta, time
from typing import Dict, List, Any
from config import Config
from platforms.factory import PlatformFactory
from hubstudio_sdk import HubStudioClient, HubStudioError
from server import ProxyServer
from db.client_mongdb import login_mongdb_review
from utils.ip_manager import IPManager
from models.buyer import BuyerInfo
import redis

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def setup_database(config: Config):
    """设置数据库连接"""
    db = login_mongdb_review()
    redis_client = redis.Redis(**config.get_redis_config())
    return db, redis_client

def handle_browser(container_code: int, client: HubStudioClient):
    """处理浏览器自动化任务"""
    try:
        browser_info, browser, playwright = client.start_playwright(
            container_code,
            args=['--start-maximized']
        )
        page = browser.contexts[0].pages[0]
        return browser, page, playwright
    except Exception as e:
        logging.error(f"操作浏览器时发生错误: {str(e)}")
        raise

def process_buyer_tasks(buyer: str, tasks: Dict[str, List[Dict[str, Any]]],
                       platform_factory: PlatformFactory, db, redis_client,
                       client: HubStudioClient):
    """处理买家任务"""
    if redis_client.exists(buyer):
        logging.info(f'buyer:{buyer}已处理，跳过')
        return
        
    # 更换IP
    ip.change(buyer)
    buyer_info = BuyerInfo(buyer, db)
    
    # 初始化浏览器
    browser, page, playwright = handle_browser(buyer_info.device_id, client)
    
    try:
        # 处理每个平台的任务
        for platform_name in ['wb', 'ozon', 'yandex']:
            platform = platform_factory.create_platform(platform_name, db, redis_client)
            
            # 检查登录状态
            if not platform.check_login(page, buyer):
                continue
                
            # 处理取货码任务
            pickup_key = f'order_pickup_{platform_name}'
            if tasks.get(pickup_key) and not redis_client.exists(f"{buyer}_{pickup_key}"):
                platform.get_pickup_code(page, buyer)
                platform.save_operate_key(buyer, pickup_key)
                
            # 处理购物任务
            shopping_key = f'order_shopping_{platform_name}'
            if tasks.get(shopping_key) and not redis_client.exists(f"{buyer}_{shopping_key}"):
                items = tasks[shopping_key]
                # 随机采样
                sample_size = max(1, int(len(items) * 0.7)) if len(items) > 3 else \
                    random.choices([1, 2], weights=[0.3, 0.7])[0] if len(items) >= 2 else 1
                sampled_items = random.sample(items, min(sample_size, len(items)))
                
                logging.info(f'开始{platform_name.upper()}购物 订单数量:{len(sampled_items)}')
                platform.buy_items(page, buyer, sampled_items)
                platform.save_operate_key(buyer, shopping_key)
                
            # 处理评论任务
            review_key = f'order_review_{platform_name}'
            if tasks.get(review_key) and not redis_client.exists(f"{buyer}_{review_key}"):
                review_items = platform.check_order_status(page, buyer)
                if review_items:
                    num_to_review = random.randint(1, min(5, len(review_items)))
                    review_items = random.sample(review_items, num_to_review)
                    logging.info(f'准备{platform_name.upper()}评论{len(review_items)}条订单')
                    platform.review_order(page, buyer, review_items)
                    platform.save_operate_key(buyer, review_key)
                    
        # 保存处理记录
        platform.save_operate_key(buyer, buyer)
        
    finally:
        page.close()
        playwright.stop()

def main():
    # 设置日志
    logger = setup_logging()
    
    # 加载配置
    config = Config()
    
    # 设置数据库
    db, redis_client = setup_database(config)
    
    # 启动代理
    proxy = ProxyServer("WLAN2", 1081)
    proxy.start()
    
    try:
        # 初始化HubStudio客户端
        hs_client = HubStudioClient(config=config)
        
        # 获取当前日期
        current_date = datetime.now().date() - timedelta(days=3)
        pre_date = datetime.combine(current_date, time(0, 0, 0))
        
        # 查询订单
        orders = db['orders'].find({
            "status": {'$in': [1, 3, 4]},
            'buyer': {'$exists': True, '$ne': ''}
        }).sort('preDate', 1)
        
        # 按买家和平台分类订单
        order_task_items = {}
        for order in orders:
            buyer = order['buyer']
            platform = order.get('platform')
            status = order.get('status')
            
            if buyer not in order_task_items:
                order_task_items[buyer] = {}
                
            botms = order_task_items[buyer]
            task_key = f'order_{"shopping" if status == 1 else "pickup" if status == 3 else "review"}_{platform}'
            
            if task_key not in botms:
                botms[task_key] = []
            botms[task_key].append(order)
            
        # 处理每个买家的任务
        platform_factory = PlatformFactory()
        for buyer, tasks in order_task_items.items():
            process_buyer_tasks(buyer, tasks, platform_factory, db, redis_client, hs_client)
            
    except HubStudioError as e:
        logger.error(f"发生错误: {e}")
    finally:
        # 关闭客户端连接
        hs_client.stop()
        # 停止代理服务
        proxy.stop()

if __name__ == '__main__':
    main() 