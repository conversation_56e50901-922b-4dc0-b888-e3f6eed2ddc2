#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件结构分析工具
用于分析主数据文件和参考格式文件的结构
"""

import pandas as pd
import openpyxl
from openpyxl.utils.dataframe import dataframe_to_rows
import os
import sys

def analyze_excel_structure(file_path, sheet_name=None):
    """
    分析Excel文件的结构

    Args:
        file_path (str): Excel文件路径
        sheet_name (str): 工作表名称，如果为None则分析所有工作表

    Returns:
        dict: 包含文件结构信息的字典
    """
    print(f"\n=== 分析文件: {file_path} ===")

    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 - {file_path}")
        return None

    try:
        # 使用openpyxl读取工作表信息
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        sheet_names = workbook.sheetnames
        print(f"工作表数量: {len(sheet_names)}")
        print(f"工作表名称: {sheet_names}")

        structure_info = {
            'file_path': file_path,
            'sheet_names': sheet_names,
            'sheets_info': {}
        }

        # 分析每个工作表
        for sheet in sheet_names:
            if sheet_name and sheet != sheet_name:
                continue

            print(f"\n--- 工作表: {sheet} ---")

            # 使用pandas读取数据
            try:
                df = pd.read_excel(file_path, sheet_name=sheet)

                sheet_info = {
                    'shape': df.shape,
                    'columns': list(df.columns),
                    'dtypes': df.dtypes.to_dict(),
                    'sample_data': df.head(3).to_dict('records') if len(df) > 0 else [],
                    'null_counts': df.isnull().sum().to_dict(),
                    'unique_counts': {}
                }

                print(f"数据形状: {df.shape[0]} 行 x {df.shape[1]} 列")
                print(f"列名: {list(df.columns)}")

                # 分析每列的数据类型和唯一值
                for col in df.columns:
                    unique_count = df[col].nunique()
                    sheet_info['unique_counts'][col] = unique_count
                    print(f"  {col}: {df[col].dtype}, 唯一值数量: {unique_count}, 空值数量: {df[col].isnull().sum()}")

                    # 如果唯一值较少，显示所有唯一值
                    if unique_count <= 20 and unique_count > 0:
                        unique_values = df[col].dropna().unique()
                        print(f"    唯一值: {list(unique_values)}")

                # 显示前几行数据样本
                print(f"\n前3行数据样本:")
                for i, row in enumerate(df.head(3).to_dict('records')):
                    print(f"  行{i+1}: {row}")

                structure_info['sheets_info'][sheet] = sheet_info

            except Exception as e:
                print(f"读取工作表 {sheet} 时出错: {e}")
                structure_info['sheets_info'][sheet] = {'error': str(e)}

        workbook.close()
        return structure_info

    except Exception as e:
        print(f"分析文件时出错: {e}")
        return None

def main():
    """主函数"""
    print("Excel文件结构分析工具")
    print("=" * 50)

    # 分析主数据文件
    main_file = "file/20250825库存情况和分配计划-对外.xlsx"
    main_structure = analyze_excel_structure(main_file)

    # 分析参考格式文件
    reference_file = "file/Дальний Восток-远东-20250825.xlsx"
    reference_structure = analyze_excel_structure(reference_file)

    # 保存分析结果
    print("\n" + "=" * 50)
    print("分析完成!")

    if main_structure:
        print(f"\n主数据文件工作表: {main_structure['sheet_names']}")
    if reference_structure:
        print(f"参考格式文件工作表: {reference_structure['sheet_names']}")

    return main_structure, reference_structure

if __name__ == "__main__":
    main_structure, reference_structure = main()